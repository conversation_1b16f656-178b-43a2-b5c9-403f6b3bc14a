// API utility functions for the ZiaHR chatbot

export const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:5051";

// Generic API request function
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  // Attach JWT token if present
  const token = localStorage.getItem('token');
  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  if (token) {
    defaultHeaders['Authorization'] = `Bearer ${token}`;
  }
  const defaultOptions: RequestInit = {
    headers: {
      ...defaultHeaders,
      ...(options.headers || {})
    },
    credentials: 'include', // Ensure cookies/session are sent for all requests
  };

  const config = { ...defaultOptions, ...options };

  try {
    const response = await fetch(url, config);

    if (!response.ok) {
      let errorMessage = `HTTP error! status: ${response.status}`;
      let errorData;
      try {
        errorData = await response.json();
        errorMessage = errorData.message || errorMessage;

        // Special handling for 2FA case - return the response data instead of throwing
        if (response.status === 401 && errorData && errorData.message === "2FA code required") {
          console.log('2FA case detected, returning response data:', errorData);
          return errorData; // Return the full response data
        }
      } catch (e) {
        // If we can't parse the error response, use the default message
      }

      throw new Error(errorMessage);
    }

    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    // Check if it's a network error
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new Error('Network error. Please check if the server is running.');
    }
    throw error;
  }
}

// Authentication API calls
export const authAPI = {
  login: async (email: string, password: string, twoFACode?: string) => {
    return apiRequest('/api/login', {
      method: 'POST',
      body: JSON.stringify({
        email,
        password,
        two_fa_code: twoFACode
      }),
    });
  },

  register: async (fullName: string, email: string, password: string, employeeId?: string) => {
    return apiRequest('/api/register', {
      method: 'POST',
      body: JSON.stringify({
        full_name: fullName,
        email,
        password,
        employee_id: employeeId
      }),
    });
  },

  logout: async () => {
    return apiRequest('/api/logout', {
      method: 'POST',
    });
  },

  verify2FA: async (code: string) => {
    return apiRequest('/api/auth/verify-2fa', {
      method: 'POST',
      body: JSON.stringify({ code }),
    });
  },

  get2FASetup: async (email: string) => {
    return apiRequest('/api/user-2fa-setup', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  },
};

// Chat API calls
export const chatAPI = {
  sendMessage: async (message: string, files?: File[], deviceId?: string, email?: string, employeeId?: string, chatId?: string) => {
    // Compose the payload as expected by /api/query
    const payload: any = {
      query: message,
      device_id: deviceId || 'web-client',
      files_info: files ? files.map((file) => ({ name: file.name, size: file.size, type: file.type })) : [],
    };
    if (email) payload.email = email;
    if (employeeId) payload.employee_id = employeeId;
    if (chatId) payload.chat_id = chatId;

    return apiRequest('/api/query', {
      method: 'POST',
      body: JSON.stringify(payload),
      headers: { 'Content-Type': 'application/json' },
    });
  },

  getChatHistory: async () => {
    return apiRequest('/api/chat/history');
  },

  deleteChat: async (chatId: string) => {
    return apiRequest(`/api/chat/${chatId}`, {
      method: 'DELETE',
    });
  },
};

// File API calls
export const fileAPI = {
  upload: async (files: File[]) => {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`file_${index}`, file);
    });

    return apiRequest('/api/files/upload', {
      method: 'POST',
      body: formData,
      headers: {}, // Remove Content-Type to let browser set it for FormData
    });
  },

  preview: async (fileId: string) => {
    return apiRequest(`/api/files/preview/${fileId}`);
  },

  summarize: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    const token = localStorage.getItem('token');
    const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
    const response = await fetch(`${API_BASE_URL}/api/summarize-document`, {
      method: 'POST',
      body: formData,
      headers,
      credentials: 'include',
    });
    if (!response.ok) {
      let errorMessage = `HTTP error! status: ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch (e) {}
      throw new Error(errorMessage);
    }
    return response.json();
  },
};

// HR Escalation API calls
export const escalationAPI = {
  submit: async (escalationData: any) => {
    return apiRequest('/api/escalation/submit', {
      method: 'POST',
      body: JSON.stringify(escalationData),
    });
  },

  getHRPersons: async () => {
    return apiRequest('/api/escalation/hr-persons');
  },
};

// User API calls
export const userAPI = {
  updateProfile: async (userData: any) => {
    return apiRequest('/api/user/profile', {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  },

  getProfile: async () => {
    return apiRequest('/api/user/profile');
  },
};
