import { useState, useEffect } from 'react';
import { User } from '@/types';
import { authAPI, userAPI } from '@/utils/api';

interface LoginResult {
  success: boolean;
  user?: User;
  token?: string;
  message?: string;
}
interface RegisterResult {
  success: boolean;
  message?: string;
  '2fa_qr_url_user'?: string;
  '2fa_qr_url_admin'?: string;
}

interface LoginCredentials {
  email: string;
  password: string;
}

const PENDING_LOGIN_STORAGE_KEY = 'pendingLogin';

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [pendingLogin, setPendingLogin] = useState<LoginCredentials | null>(() => {
    try {
      const stored = sessionStorage.getItem(PENDING_LOGIN_STORAGE_KEY);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error("Failed to parse pending login from session storage:", error);
      return null;
    }
  });

  // Log and persist when pendingLogin is set or cleared
  useEffect(() => {
    console.log('pendingLogin state changed:', pendingLogin);
    if (pendingLogin) {
      sessionStorage.setItem(PENDING_LOGIN_STORAGE_KEY, JSON.stringify(pendingLogin));
    } else {
      sessionStorage.removeItem(PENDING_LOGIN_STORAGE_KEY);
    }
  }, [pendingLogin]);

  // Robust, synchronous checkAuthStatus
  const checkAuthStatus = async () => {
    try {
      setIsLoading(true);
      console.log('checkAuthStatus called');
      const savedUser = localStorage.getItem('user');
      const token = localStorage.getItem('token');
      // JWT expiry check
      let isTokenValid = false;
      if (token) {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          if (payload.exp && payload.exp * 1000 > Date.now()) {
            isTokenValid = true;
          }
        } catch (e) {
          isTokenValid = false;
        }
      }
      if (!token || !isTokenValid) {
        // Token missing or expired: logout and redirect
        setUser(null);
        localStorage.removeItem('user');
        localStorage.removeItem('token');
        localStorage.removeItem('chatSessions');
        localStorage.removeItem('currentSessionId');
        document.body.classList.add('not-logged-in');
        document.body.classList.remove('logged-in');
        document.documentElement.classList.add('not-logged-in');
        document.documentElement.classList.remove('logged-in');
        // Only redirect if not already on login or pre-login UI
        const path = window.location.pathname;
        if (path !== '/login' && path !== '/' && !path.startsWith('/register')) {
          window.location.href = '/login';
        }
        return;
      }
      if (savedUser) {
        try {
          let userData = JSON.parse(savedUser);
          // Fetch latest profile from backend and merge
          try {
            const profileResp = await userAPI.getProfile();
            if (profileResp && typeof profileResp === 'object' && 'success' in profileResp && profileResp.success && 'profile' in profileResp && profileResp.profile) {
              userData = { ...userData, ...profileResp.profile };
              localStorage.setItem('user', JSON.stringify(userData));
            }
          } catch (e) {
            // Ignore profile fetch errors, fallback to local
          }
          setUser(userData);
          document.body.classList.remove('not-logged-in');
          document.body.classList.add('logged-in');
          document.documentElement.classList.remove('not-logged-in');
          document.documentElement.classList.add('logged-in');
          console.log('User found in localStorage:', userData);
        } catch (parseError) {
          console.error('Failed to parse user from localStorage:', parseError);
          setUser(null);
          localStorage.removeItem('user');
        }
      } else {
        setUser(null);
        document.body.classList.add('not-logged-in');
        document.body.classList.remove('logged-in');
        document.documentElement.classList.add('not-logged-in');
        document.documentElement.classList.remove('logged-in');
        console.log('No user in localStorage');
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      setUser(null);
    } finally {
      setIsLoading(false);
      console.log('isLoading set to false');
    }
  };

  const login = async (email: string, password: string) => {
    try {
      // Clear chat sessions before login to prevent leakage
      localStorage.removeItem('chatSessions');
      localStorage.removeItem('currentSessionId');
      const result = await authAPI.login(email, password) as LoginResult;
      if (typeof result === 'object' && result !== null && 'success' in result) {
        if (result.success) {
          if (!result.user) {
            return { success: false, error: 'User data missing from response' };
          }
          let userData: User = result.user;
          // Fetch latest profile from backend and merge
          try {
            const profileResp = await userAPI.getProfile();
            if (profileResp && typeof profileResp === 'object' && 'success' in profileResp && profileResp.success && 'profile' in profileResp && profileResp.profile) {
              userData = { ...userData, ...profileResp.profile };
            }
          } catch (e) {
            // Ignore profile fetch errors, fallback to login response
          }
          setUser(userData);
          localStorage.setItem('user', JSON.stringify(userData));
          if (typeof result.token === 'string') {
            localStorage.setItem('token', result.token);
          }
          // Fetch chat history for the authenticated user and populate state
          if (window.__CHAT_HOOK__ && typeof window.__CHAT_HOOK__.loadUserChatHistory === 'function') {
            await window.__CHAT_HOOK__.loadUserChatHistory();
          }
          console.log('Login successful, clearing pendingLogin.');
          setPendingLogin(null); // Clear pending login on successful login
          document.body.classList.remove('not-logged-in');
          document.body.classList.add('logged-in');
          document.documentElement.classList.remove('not-logged-in');
          document.documentElement.classList.add('logged-in');
          return { success: true };
        }
        // Check if 2FA is required
        if (result.message === "2FA code required") {
          console.log('2FA required, setting pendingLogin for:', email);
          setPendingLogin({ email, password });
          return { success: false, require2FA: true, error: result.message };
        }
        // Custom error for account not found
        if (result.message === "Email User Not Found") {
          return { success: false, error: 'No account found with this email. Please register.' };
        }
        return { success: false, error: result.message || 'Login failed' };
      }
      else {
        return { success: false, error: 'Invalid response from login API' };
      }
    } catch (error: any) {
      // Try to extract backend error message
      let errorMsg = 'Network error. Please try again.';
      if (error && error.message) {
        errorMsg = error.message;
      }
      return { success: false, error: errorMsg };
    }
  };

  const register = async (fullName: string, email: string, password: string, employeeId?: string) => {
    try {
      const result = await authAPI.register(fullName, email, password, employeeId) as RegisterResult;
      if (typeof result === 'object' && result !== null && 'success' in result) {
        if (result.success) {
          // Registration successful, return QR codes for 2FA setup
          return {
            success: true,
            qrCodeUser: result['2fa_qr_url_user'],
            qrCodeAdmin: result['2fa_qr_url_admin'],
            message: result.message
          };
        } else {
          return { success: false, error: result.message || 'Registration failed' };
        }
      }
      else {
        return { success: false, error: 'Invalid response from register API' };
      }
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: 'Network error. Please try again.' };
    }
  };

  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (e) {
      // Ignore errors on logout
    }
    setUser(null);
    console.log('Logout, clearing pendingLogin.');
    setPendingLogin(null);
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    // Clear chat sessions on logout
    localStorage.removeItem('chatSessions');
    localStorage.removeItem('currentSessionId');
    document.body.classList.add('not-logged-in');
    document.body.classList.remove('logged-in');
    document.documentElement.classList.add('not-logged-in');
    document.documentElement.classList.remove('logged-in');
  };

  const verify2FA = async (code: string) => {
    try {
      console.log('verify2FA: pendingLogin before check:', pendingLogin);
      if (!pendingLogin) {
        console.error('verify2FA: pendingLogin is null, returning error.');
        return { success: false, error: 'No pending login found' };
      }
      // Only call /api/auth/verify-2fa, do NOT call login again
      const result = await authAPI.verify2FA(code) as LoginResult;
      if (typeof result === 'object' && result !== null && 'success' in result) {
        if (result.success) {
          if (!result.user) {
            return { success: false, error: 'User data missing from response' };
          }
          const userData: User = result.user;
          setUser(userData);
          localStorage.setItem('user', JSON.stringify(userData));
          if (typeof result.token === 'string') {
            localStorage.setItem('token', result.token);
          }
          console.log('2FA verification successful, clearing pendingLogin.');
          setPendingLogin(null);
          document.body.classList.remove('not-logged-in');
          document.body.classList.add('logged-in');
          document.documentElement.classList.remove('not-logged-in');
          document.documentElement.classList.add('logged-in');
          return { success: true };
        } else {
          console.error('2FA verification failed:', result.message);
          return { success: false, error: result.message || '2FA verification failed' };
        }
      } else {
        console.error('Invalid response from verify2FA API.');
        return { success: false, error: 'Invalid response from verify2FA API' };
      }
    } catch (error: any) {
      console.error('2FA verification error:', error);
      let errorMsg = 'Network error. Please try again.';
      if (error && error.message) {
        errorMsg = error.message;
      }
      return { success: false, error: errorMsg };
    }
  };

  const get2FASetup = async (email: string) => {
    try {
      const result = await authAPI.get2FASetup(email);
      return result;
    } catch (error) {
      console.error('2FA setup error:', error);
      return { success: false, error: 'Failed to get 2FA setup' };
    }
  };

  const updateUser = (updates: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...updates };
      setUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
  };

  return {
    user,
    isLoading,
    isLoggedIn: !!user,
    login,
    register,
    logout,
    updateUser,
    checkAuthStatus,
    verify2FA,
    get2FASetup,
    pendingLogin,
  };
};
