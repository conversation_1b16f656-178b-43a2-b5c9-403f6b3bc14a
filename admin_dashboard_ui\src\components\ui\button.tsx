import React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "xs" | "default" | "sm" | "lg" | "icon";
  animate?: boolean;
}

const getVariantClasses = (variant: string = "default") => {
  const variants = {
    default: "bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm hover:shadow-md",
    destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md",
    outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground shadow-sm hover:shadow-md",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm hover:shadow-md",
    ghost: "hover:bg-accent hover:text-accent-foreground",
    link: "text-primary underline-offset-4 hover:underline",
  };
  return variants[variant as keyof typeof variants] || variants.default;
};

const getSizeClasses = (size: string = "default") => {
  const sizes = {
    xs: "h-7 px-2 py-1 text-xs",
    default: "h-10 px-4 py-2",
    sm: "h-9 rounded-md px-3",
    lg: "h-11 rounded-md px-8",
    icon: "h-10 w-10",
  };
  return sizes[size as keyof typeof sizes] || sizes.default;
};

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = "default", size = "default", animate = true, children, ...props }, ref) => {
    // Omit onDrag to fix framer-motion typing error
    const { onDrag, ...restProps } = props as any;
    if (animate) {
      return (
        <motion.button
          className={cn(
            "inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium",
            "ring-offset-background transition-all duration-200 focus-visible:outline-none",
            "focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
            "disabled:pointer-events-none disabled:opacity-50 transform-gpu",
            getVariantClasses(variant),
            getSizeClasses(size),
            className
          )}
          ref={ref}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          transition={{ duration: 0.1, ease: "easeOut" }}
          {...restProps}
        >
          {children}
        </motion.button>
      );
    }

    return (
      <button
        className={cn(
          "inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium",
          "ring-offset-background transition-all duration-200 focus-visible:outline-none",
          "focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          "disabled:pointer-events-none disabled:opacity-50 transform-gpu",
          getVariantClasses(variant),
          getSizeClasses(size),
          className
        )}
        ref={ref}
        {...props}
      >
        {children}
      </button>
    );
  }
);
Button.displayName = "Button";