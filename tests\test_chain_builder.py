"""
Comprehensive unit tests for the ChainBuilder module.
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import numpy as np

from src.chain.chain_builder import <PERSON><PERSON>uilder, is_pure_greeting, sanitize_input, contains_sensitive_info


class TestChainBuilderUtils:
    """Test utility functions in ChainBuilder."""
    
    def test_is_pure_greeting(self):
        """Test greeting detection."""
        assert is_pure_greeting("hi") == True
        assert is_pure_greeting("hello") == True
        assert is_pure_greeting("hey there") == True
        assert is_pure_greeting("good morning") == True
        assert is_pure_greeting("What is the leave policy?") == False
        assert is_pure_greeting("") == False
    
    def test_sanitize_input(self):
        """Test input sanitization."""
        assert sanitize_input("  test input  ") == "test input"
        assert sanitize_input("test\ninput") == "test input"
        assert sanitize_input("test\r\ninput") == "test input"
        assert sanitize_input("test\tinput") == "test input"
        assert sanitize_input("") == ""
    
    def test_contains_sensitive_info(self):
        """Test sensitive information detection."""
        assert contains_sensitive_info("My salary is 50000") == True
        assert contains_sensitive_info("My PAN number is **********") == True
        assert contains_sensitive_info("My Aadhaar is 123456789012") == True
        assert contains_sensitive_info("What is the leave policy?") == False
        assert contains_sensitive_info("") == False


class TestChainBuilderInitialization:
    """Test ChainBuilder initialization."""
    
    def test_chain_builder_init_success(self, mock_env_vars):
        """Test successful ChainBuilder initialization."""
        with patch('src.chain.chain_builder.ChatGroq') as mock_groq, \
             patch('src.chain.chain_builder.ContextBuilder') as mock_cb, \
             patch('src.chain.chain_builder.HistoryManager') as mock_hm, \
             patch('src.chain.chain_builder.EmailService') as mock_es, \
             patch('src.chain.chain_builder.IntentClassifier') as mock_ic, \
             patch('src.chain.chain_builder.EntityExtractor') as mock_ee, \
             patch('src.chain.chain_builder.DocumentVersionControl') as mock_vc:
            
            mock_groq.return_value = Mock()
            mock_cb.return_value = Mock()
            mock_hm.return_value = Mock()
            mock_es.return_value = Mock()
            mock_ic.return_value = Mock()
            mock_ee.return_value = Mock()
            mock_vc.return_value = Mock()
            
            builder = ChainBuilder()
            
            assert builder.model_name == "llama3-8b-8192"
            assert builder.api_key == "test_groq_key"
            assert builder.context_builder is not None
            assert builder.history_manager is not None
            assert builder.email_service is not None
            assert builder.intent_classifier is not None
            assert builder.entity_extractor is not None
    
    def test_chain_builder_init_missing_api_key(self):
        """Test ChainBuilder initialization with missing API key."""
        with patch.dict('os.environ', {}, clear=True):
            with pytest.raises(ValueError, match="GROQ_API_KEY is required"):
                ChainBuilder()
    
    def test_chain_builder_init_missing_model_name(self):
        """Test ChainBuilder initialization with missing model name."""
        with patch.dict('os.environ', {'GROQ_API_KEY': 'test'}, clear=True):
            with patch('src.chain.chain_builder.LLM_MODEL_NAME', None):
                with pytest.raises(ValueError, match="LLM_MODEL_NAME is required"):
                    ChainBuilder()


class TestChainBuilderFormatResponse:
    """Test response formatting functionality."""
    
    def test_format_response_empty(self, chain_builder):
        """Test formatting empty response."""
        result = chain_builder.format_response("")
        assert result == "I'm sorry, I couldn't find a helpful response."
    
    def test_format_response_normal(self, chain_builder):
        """Test formatting normal response."""
        response = "This is a normal response with multiple sentences. It should be formatted properly."
        result = chain_builder.format_response(response)
        assert result == response
    
    def test_format_response_with_headers(self, chain_builder):
        """Test formatting response with headers."""
        response = "What is the leave policy?\n\nEmployees get 20 days of annual leave."
        result = chain_builder.format_response(response)
        assert "### What is the leave policy?" in result
    
    def test_format_response_with_bullets(self, chain_builder):
        """Test formatting response with bullet points."""
        response = "Benefits include:\n- Health insurance\n- Dental coverage\n- Vision care"
        result = chain_builder.format_response(response)
        assert "- Health insurance" in result
        assert "- Dental coverage" in result


class TestChainBuilderRunChain:
    """Test the main run_chain method."""
    
    @pytest.mark.asyncio
    async def test_run_chain_success(self, chain_builder, sample_queries):
        """Test successful chain execution."""
        query = sample_queries[0]
        device_id = "test_device_123"
        
        # Mock all dependencies
        chain_builder.api_status_checker.is_groq_operational.return_value = True
        chain_builder.context_builder.build_context.return_value = Mock(
            context="Sample context",
            sources=[{"content": "Sample source", "score": 0.85}]
        )
        chain_builder.history_manager.get_history.return_value = []
        
        with patch('src.chain.chain_builder.create_hr_assistant_prompt') as mock_prompt, \
            patch('src.chain.chain_builder.detect_response_mode', return_value='auto') as mock_detect_mode, \
            patch('src.chain.chain_builder.should_include_reasoning', return_value=True) as mock_reasoning, \
             patch('asyncio.to_thread') as mock_thread:
            
            mock_prompt.return_value.invoke.return_value = "Formatted prompt"
            mock_thread.side_effect = ["Formatted prompt", Mock(content="Test response")]
            
            result = await chain_builder.run_chain(query, device_id)
            
            assert result["content"] is not None
            assert result["language"] == "en"
            assert "sources" in result
            assert "response_time" in result
    
    @pytest.mark.asyncio
    async def test_run_chain_groq_unavailable(self, chain_builder, sample_queries):
        """Test chain execution when Groq API is unavailable."""
        query = sample_queries[0]
        device_id = "test_device_123"
        
        chain_builder.api_status_checker.is_groq_operational.return_value = False
        
        result = await chain_builder.run_chain(query, device_id)
        
        assert "service is currently unavailable" in result["content"]
        assert result["error"]["type"] == "ServiceUnavailable"
    
    @pytest.mark.asyncio
    async def test_run_chain_pure_greeting(self, chain_builder):
        """Test chain execution with pure greeting."""
        query = "hi"
        device_id = "test_device_123"
        
        chain_builder.api_status_checker.is_groq_operational.return_value = True
        chain_builder.intent_classifier.classify.return_value = ("greeting", 0.3)
        
        result = await chain_builder.run_chain(query, device_id)
        
        assert "Hello! How can I assist you" in result["content"]
        assert result["intent"] == "greeting"
        assert result["intent_confidence"] == 1.0
    
    @pytest.mark.asyncio
    async def test_run_chain_with_files_info(self, chain_builder, sample_queries):
        """Test chain execution with file information."""
        query = sample_queries[0]
        device_id = "test_device_123"
        files_info = [{"name": "test.pdf", "path": "/path/to/test.pdf"}]
        
        chain_builder.api_status_checker.is_groq_operational.return_value = True
        chain_builder.context_builder.build_context.return_value = Mock(
            context="Sample context",
            sources=[{"content": "Sample source", "score": 0.85}]
        )
        chain_builder.history_manager.get_history.return_value = []
        chain_builder.version_control.check_version.return_value = True
        chain_builder.version_control.reindex_document.return_value = None
        
        with patch('src.chain.chain_builder.create_hr_assistant_prompt') as mock_prompt, \
             patch('asyncio.to_thread') as mock_thread:
            
            mock_prompt.return_value.invoke.return_value = "Formatted prompt"
            mock_thread.side_effect = ["Formatted prompt", Mock(content="Test response")]
            
            result = await chain_builder.run_chain(query, device_id, files_info)
            
            assert result["content"] is not None
            chain_builder.version_control.check_version.assert_called_once()
            chain_builder.version_control.reindex_document.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_run_chain_escalation_detected(self, chain_builder, sample_queries):
        """Test chain execution with escalation detection."""
        query = sample_queries[0]
        device_id = "test_device_123"
        
        chain_builder.api_status_checker.is_groq_operational.return_value = True
        chain_builder.context_builder.build_context.return_value = Mock(
            context="Sample context",
            sources=[{"content": "Sample source", "score": 0.85}]
        )
        chain_builder.history_manager.get_history.return_value = []
        chain_builder.enable_email_escalation = True
        chain_builder.hr_emails = ["<EMAIL>"]
        
        with patch('src.chain.chain_builder.create_hr_assistant_prompt') as mock_prompt, \
             patch('asyncio.to_thread') as mock_thread:
            
            mock_prompt.return_value.invoke.return_value = "Formatted prompt"
            mock_thread.side_effect = ["Formatted prompt", Mock(content="Response [ESCALATE_TO_HR] with escalation")]
            
            result = await chain_builder.run_chain(query, device_id)
            
            assert result["escalated"] == True
            assert "[ESCALATE_TO_HR]" not in result["content"]
            assert "Would you like me to escalate" in result["content"]
    
    @pytest.mark.asyncio
    async def test_run_chain_retry_on_failure(self, chain_builder, sample_queries):
        """Test chain execution with retry on LLM failure."""
        query = sample_queries[0]
        device_id = "test_device_123"
        
        chain_builder.api_status_checker.is_groq_operational.return_value = True
        chain_builder.context_builder.build_context.return_value = Mock(
            context="Sample context",
            sources=[{"content": "Sample source", "score": 0.85}]
        )
        chain_builder.history_manager.get_history.return_value = []
        
        with patch('src.chain.chain_builder.create_hr_assistant_prompt') as mock_prompt, \
             patch('asyncio.to_thread') as mock_thread, \
             patch('asyncio.sleep') as mock_sleep:
            
            mock_prompt.return_value.invoke.return_value = "Formatted prompt"
            # First call fails, second succeeds
            mock_thread.side_effect = [
                "Formatted prompt",
                Exception("LLM Error"),
                "Formatted prompt",
                Mock(content="Test response")
            ]
            
            result = await chain_builder.run_chain(query, device_id)
            
            assert result["content"] is not None
            assert mock_sleep.called  # Verify retry delay was called
    
    @pytest.mark.asyncio
    async def test_run_chain_max_retries_exceeded(self, chain_builder, sample_queries):
        """Test chain execution when max retries are exceeded."""
        query = sample_queries[0]
        device_id = "test_device_123"
        
        chain_builder.api_status_checker.is_groq_operational.return_value = True
        chain_builder.context_builder.build_context.return_value = Mock(
            context="Sample context",
            sources=[{"content": "Sample source", "score": 0.85}]
        )
        chain_builder.history_manager.get_history.return_value = []
        
        with patch('src.chain.chain_builder.create_hr_assistant_prompt') as mock_prompt, \
             patch('asyncio.to_thread') as mock_thread, \
             patch('asyncio.sleep') as mock_sleep:
            
            mock_prompt.return_value.invoke.return_value = "Formatted prompt"
            mock_thread.side_effect = Exception("Persistent LLM Error")
            
            result = await chain_builder.run_chain(query, device_id)
            
            assert "something went wrong" in result["content"]
            assert result["error"]["type"] == "Exception"
            assert result["error"]["retry_attempted"] == True


class TestChainBuilderErrorHandling:
    """Test error handling in ChainBuilder."""
    
    @pytest.mark.asyncio
    async def test_error_method(self, chain_builder):
        """Test the _error method."""
        error = Exception("Test error")
        result = await chain_builder._error("TestError", error, 1)
        
        assert result["content"] is not None
        assert result["language"] == "en"
        assert result["sources"] == []
        assert result["error"]["type"] == "TestError"
        assert result["error"]["message"] == "Test error"
        assert result["error"]["retry_attempted"] == True
    
    @pytest.mark.asyncio
    async def test_error_method_timeout(self, chain_builder):
        """Test error method with timeout error."""
        error = Exception("timeout error")
        result = await chain_builder._error("TimeoutError", error, 0)
        
        assert "timed out" in result["content"]
    
    @pytest.mark.asyncio
    async def test_error_method_api_key(self, chain_builder):
        """Test error method with API key error."""
        error = Exception("api key error")
        result = await chain_builder._error("APIKeyError", error, 0)
        
        assert "Invalid API key" in result["content"]


class TestChainBuilderVectorDatabase:
    """Test vector database functionality."""
    
    def test_get_vector_database_count_success(self, chain_builder, mock_vector_store):
        """Test successful vector count retrieval."""
        mock_vector_store.client.get_collection.return_value = Mock(points_count=100)
        
        count = chain_builder.get_vector_database_count()
        
        assert count == 100
    
    def test_get_vector_database_count_fallback(self, chain_builder, mock_vector_store):
        """Test vector count retrieval with fallback."""
        mock_vector_store.client.get_collection.side_effect = Exception("Qdrant error")
        mock_vector_store.similarity_search.return_value = [{"content": "test"}]
        
        count = chain_builder.get_vector_database_count()
        
        assert count == 1
    
    def test_get_vector_database_count_error(self, chain_builder, mock_vector_store):
        """Test vector count retrieval with error."""
        mock_vector_store.client.get_collection.side_effect = Exception("Qdrant error")
        mock_vector_store.similarity_search.side_effect = Exception("Search error")
        
        count = chain_builder.get_vector_database_count()
        
        assert count == 0


class TestChainBuilderIntegration:
    """Integration tests for ChainBuilder."""
    
    @pytest.mark.asyncio
    async def test_full_chain_integration(self, chain_builder, sample_queries):
        """Test full chain integration with all components."""
        query = sample_queries[0]
        device_id = "test_device_123"
        
        # Setup all mocks
        chain_builder.api_status_checker.is_groq_operational.return_value = True
        chain_builder.context_builder.build_context.return_value = Mock(
            context="Employees are entitled to 20 days of annual leave per year.",
            sources=[{"content": "Leave policy content", "score": 0.85}]
        )
        chain_builder.history_manager.get_history.return_value = []
        chain_builder.intent_classifier.classify.return_value = ("policy_inquiry", 0.9)
        chain_builder.entity_extractor.extract_entities.return_value = [
            {"text": "leave", "label": "POLICY_TYPE", "confidence": 0.9}
        ]
        
        with patch('src.chain.chain_builder.create_hr_assistant_prompt') as mock_prompt, \
             patch('asyncio.to_thread') as mock_thread:
            
            mock_prompt.return_value.invoke.return_value = "Formatted prompt"
            mock_thread.side_effect = ["Formatted prompt", Mock(content="Based on the leave policy, employees get 20 days of annual leave.")]
            
            result = await chain_builder.run_chain(query, device_id)
            
            # Verify all components were called
            assert result["content"] is not None
            assert result["language"] == "en"
            assert len(result["sources"]) > 0
            assert result["intent"] == "policy_inquiry"
            assert result["intent_confidence"] == 0.9
            assert len(result["entities"]) > 0
            assert "response_time" in result
    
    @pytest.mark.asyncio
    async def test_chain_with_history(self, chain_builder, sample_queries):
        """Test chain with conversation history."""
        query = sample_queries[0]
        device_id = "test_device_123"
        
        # Setup history
        history = [
            {
                "user_query": "What is the company policy?",
                "assistant_response": "The company has various policies including leave, dress code, and remote work.",
                "language": "en",
                "timestamp": 1234567890.0
            }
        ]
        
        chain_builder.api_status_checker.is_groq_operational.return_value = True
        chain_builder.context_builder.build_context.return_value = Mock(
            context="Sample context",
            sources=[{"content": "Sample source", "score": 0.85}]
        )
        chain_builder.history_manager.get_history.return_value = history
        
        with patch('src.chain.chain_builder.create_hr_assistant_prompt') as mock_prompt, \
             patch('asyncio.to_thread') as mock_thread:
            
            mock_prompt.return_value.invoke.return_value = "Formatted prompt"
            mock_thread.side_effect = ["Formatted prompt", Mock(content="Test response")]
            
            result = await chain_builder.run_chain(query, device_id)
            
            assert result["content"] is not None
            # Verify history was retrieved
            chain_builder.history_manager.get_history.assert_called_with(device_id)


class TestChainBuilderPerformance:
    """Performance tests for ChainBuilder."""
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_chain_performance(self, chain_builder, sample_queries):
        """Test chain performance with multiple queries."""
        device_id = "test_device_123"
        
        chain_builder.api_status_checker.is_groq_operational.return_value = True
        chain_builder.context_builder.build_context.return_value = Mock(
            context="Sample context",
            sources=[{"content": "Sample source", "score": 0.85}]
        )
        chain_builder.history_manager.get_history.return_value = []
        
        with patch('src.chain.chain_builder.create_hr_assistant_prompt') as mock_prompt, \
             patch('asyncio.to_thread') as mock_thread:
            
            mock_prompt.return_value.invoke.return_value = "Formatted prompt"
            mock_thread.side_effect = ["Formatted prompt", Mock(content="Test response")]
            
            import time
            start_time = time.time()
            
            # Process multiple queries
            for query in sample_queries[:5]:
                result = await chain_builder.run_chain(query, device_id)
                assert result["content"] is not None
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Should complete within reasonable time (adjust based on your requirements)
            assert total_time < 10.0  # 10 seconds for 5 queries
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_chain_memory_usage(self, chain_builder, sample_queries):
        """Test chain memory usage."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        device_id = "test_device_123"
        
        chain_builder.api_status_checker.is_groq_operational.return_value = True
        chain_builder.context_builder.build_context.return_value = Mock(
            context="Sample context",
            sources=[{"content": "Sample source", "score": 0.85}]
        )
        chain_builder.history_manager.get_history.return_value = []
        
        with patch('src.chain.chain_builder.create_hr_assistant_prompt') as mock_prompt, \
             patch('asyncio.to_thread') as mock_thread:
            
            mock_prompt.return_value.invoke.return_value = "Formatted prompt"
            mock_thread.side_effect = ["Formatted prompt", Mock(content="Test response")]
            
            # Process multiple queries
            for query in sample_queries[:10]:
                result = await chain_builder.run_chain(query, device_id)
                assert result["content"] is not None
            
            final_memory = process.memory_info().rss
            memory_increase = final_memory - initial_memory
            
            # Memory increase should be reasonable (adjust based on your requirements)
            assert memory_increase < 100 * 1024 * 1024  # 100MB 