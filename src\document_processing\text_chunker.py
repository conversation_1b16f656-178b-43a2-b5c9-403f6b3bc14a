"""
Production-grade text chunker with token awareness, multiple strategies, and enterprise features.
"""
import re
import unicodedata
import asyncio
import time
from typing import List, Dict, Any, Optional, Generator, Tuple, Union, Callable, Literal
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from concurrent.futures import ThreadPoolExecutor
import logging
from pathlib import Path
import hashlib
import json
from src.chunking_strategies.base import ChunkingStrategy, ChunkingConfig, TokenizerManager
from src.chunking_strategies.token_aware import TokenAwareStrategy
from src.chunking_strategies.semantic import SemanticStrategy
from src.chunking_strategies.hr import HRStrategy
from src.chunking_strategies.recursive import RecursiveStrategy
from src.utils.logger import get_logger
import os
SPACY_MODEL_PATH = os.path.abspath("data/models/spacy/en_core_web_sm/en_core_web_sm-3.8.0")
logger = get_logger(__name__)

# Optional imports with fallbacks
try:
    from transformers import AutoTokenizer
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    logger.warning("transformers not available - advanced tokenization disabled")

try:
    from langchain.text_splitter import RecursiveCharacterTextSplitter, TokenTextSplitter
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logger.warning("langchain not available - external splitters disabled")

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    logger.warning("spacy not available - advanced NLP features disabled")

try:
    from sentence_transformers import SentenceTransformer
    import numpy as np
    from sklearn.metrics.pairwise import cosine_similarity
    SEMANTIC_AVAILABLE = True
except ImportError:
    SEMANTIC_AVAILABLE = False
    logger.warning("sentence-transformers/sklearn not available - semantic chunking disabled")


@dataclass
class ChunkingMetrics:
    """Metrics for monitoring chunking performance."""
    total_chunks: int = 0
    total_tokens: int = 0
    total_characters: int = 0
    processing_time: float = 0.0
    average_chunk_size: float = 0.0
    token_efficiency: float = 0.0
    strategy_used: str = ""
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


class ChunkingError(Exception):
    """Enhanced chunking exception with context."""
    def __init__(self, message: str, context: Optional[Dict] = None):
        super().__init__(message)
        self.context = context or {}


class StrategySelector:
    """Intelligent strategy selection based on document characteristics."""
    
    def select_strategy(self, text: str, config: ChunkingConfig) -> ChunkingStrategy:
        """Select optimal chunking strategy based on document analysis."""
        if config.strategy != "auto":
            return self._get_strategy_by_name(config.strategy)
        
        # Analyze document characteristics
        line_count = len(text.split('\n'))
        avg_line_length = len(text) / line_count if line_count > 0 else 0
        header_ratio = self._calculate_header_ratio(text)
        
        # Decision logic
        if config.hr_mode and header_ratio > 0.1:  # Many headers - likely structured document
            return HRStrategy()
        elif SEMANTIC_AVAILABLE and len(text) > 5000:  # Large document - use semantic
            return SemanticStrategy()
        elif avg_line_length > 100:  # Long lines - likely prose
            return TokenAwareStrategy()
        else:  # Default to recursive
            return RecursiveStrategy()
    
    def _get_strategy_by_name(self, name: str) -> ChunkingStrategy:
        """Get strategy instance by name."""
        strategies = {
            "token": TokenAwareStrategy(),
            "semantic": SemanticStrategy(),
            "recursive": RecursiveStrategy(),
            "hr": HRStrategy(),
            "sentence": TokenAwareStrategy(),  # Alias
        }
        return strategies.get(name, TokenAwareStrategy())
    
    def _calculate_header_ratio(self, text: str) -> float:
        """Calculate ratio of header lines to total lines."""
        lines = text.split('\n')
        header_patterns = [
            r'^#{1,6}\s+', r'^\d+\.\s+', r'^[A-Z][A-Z\s]{2,}:',
            r'^(?:Policy|Procedure|Section|Chapter)\s+'
        ]
        
        header_count = 0
        for line in lines:
            if any(re.match(pattern, line.strip()) for pattern in header_patterns):
                header_count += 1
        
        return header_count / len(lines) if lines else 0


class TextChunker:
    """Production-grade text chunker with enterprise features."""
    
    def __init__(self, config: Optional[ChunkingConfig] = None, chunk_size=None, chunk_overlap=None):
        self.config = config or ChunkingConfig()
        if chunk_size:
            self.config.chunk_size = chunk_size
        if chunk_overlap:
            self.config.chunk_overlap = chunk_overlap
        
        # Initialize components
        self.tokenizer_manager = TokenizerManager()
        self.strategy_selector = StrategySelector()
        self.metrics = ChunkingMetrics()
        self.executor = ThreadPoolExecutor(max_workers=self.config.max_workers)
        
        # Performance tracking
        self._chunk_cache = {}
        self._processing_stats = {"total_documents": 0, "total_time": 0.0}
        self._last_strategy_used = 'unknown' # Initialize for metadata reporting
    
    async def chunk_document_async(self, document: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Async version of chunk_document for high-throughput scenarios."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self.chunk_document, document)
    
    def chunk_document(self, document: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Enhanced document chunking with comprehensive error handling and metrics."""
        start_time = time.time()
        try:
            # Input validation
            content = document.get("content")
            if not isinstance(document, dict) or not isinstance(content, str):
                raise ChunkingError("Invalid document format", {"document_keys": list(document.keys())})

            # Text preprocessing
            content = self._normalize_text(content)
            if not content:
                logger.warning("Empty normalized content")
                return []

            # Check cache for repeated content
            cache_key = hashlib.md5(content.encode()).hexdigest()
            if self.config.cache_tokenization and cache_key in self._chunk_cache:
                logger.info(f"Returning cached chunks for document")
                return self._create_chunk_dicts_from_cache(document, cache_key)

            # Early return for small documents
            if len(content) < self.config.min_chunk_size:
                logger.info(f"Document too short to chunk: {len(content)} chars")
                chunk_dict = self._create_chunk_dict(document, content, 0, 1)
                self._update_metrics(start_time, [content], "single_chunk")
                return [chunk_dict]

            # Strategy selection and chunking
            strategy = self.strategy_selector.select_strategy(content, self.config)
            logger.info(f"Using strategy: {strategy.get_name()}")
            self._last_strategy_used = strategy.get_name() # Store for metadata

            # Special handling for HRStrategy: return dicts directly
            if isinstance(strategy, HRStrategy):
                raw_chunks = strategy.chunk_text(content, self.config, self.tokenizer_manager)
                chunks = [
                    {
                        "content": chunk.strip(),
                        "chunk_index": idx,
                        "title": document.get("title", ""),
                        "source_file": document.get("source_file", ""),
                        "strategy": strategy.__class__.__name__,
                    }
                    for idx, chunk in enumerate(raw_chunks)
                    if chunk.strip()
                ]
                self._update_metrics(start_time, [c["content"] for c in chunks], strategy.get_name())
                return chunks

            chunks = list(self._split_text(content, strategy))
            if not chunks:
                logger.warning("No chunks produced")
                return []

            # Quality validation and filtering
            final_chunks = self._validate_and_filter_chunks(chunks)

            # Cache results
            if self.config.cache_tokenization:
                self._chunk_cache[cache_key] = final_chunks

            # Create final chunk dictionaries
            result = [self._create_chunk_dict(document, chunk, i, len(final_chunks)) 
                      for i, chunk in enumerate(final_chunks)]

            # Update metrics
            self._update_metrics(start_time, final_chunks, strategy.get_name())

            return result
        except Exception as e:
            self.metrics.errors.append(str(e))
            logger.error(f"Chunking failed: {str(e)}")
            raise ChunkingError(str(e), {"document_id": document.get("id", "unknown")}) from e
    
    def _validate_and_filter_chunks(self, chunks: List[str]) -> List[str]:
        """Validate chunks and filter out invalid ones."""
        valid_chunks = []
        
        for i, chunk in enumerate(chunks):
            chunk = chunk.strip()
            
            # Size validation
            if len(chunk) < self.config.min_chunk_size:
                self.metrics.warnings.append(f"Chunk {i} too short: {len(chunk)} chars")
                continue
            
            # Token validation (if enabled)
            if self.config.use_tokens:
                token_count = self.tokenizer_manager.count_tokens(chunk, self.config.tokenizer_name)
                if token_count > self.config.max_tokens:
                    self.metrics.warnings.append(f"Chunk {i} exceeds token limit: {token_count} tokens")
                    # Try to salvage by re-chunking
                    strategy = TokenAwareStrategy()
                    sub_chunks = strategy.chunk_text(chunk, self.config, self.tokenizer_manager)
                    valid_chunks.extend(sub_chunks)
                    continue
            
            # Content validation
            if not chunk or chunk.isspace():
                self.metrics.warnings.append(f"Chunk {i} is empty or whitespace only")
                continue
            
            valid_chunks.append(chunk)
        
        return valid_chunks
    
    def _split_text(self, text: str, strategy: ChunkingStrategy) -> Generator[str, None, None]:
        """Split text using the selected strategy."""
        try:
            chunks = strategy.chunk_text(text, self.config, self.tokenizer_manager)
            for chunk in chunks:
                if chunk and chunk.strip():
                    yield chunk.strip()
        except Exception as e:
            logger.error(f"Strategy {strategy.get_name()} failed: {e}")
            # Fallback to basic token-aware chunking
            fallback_strategy = TokenAwareStrategy()
            chunks = fallback_strategy.chunk_text(text, self.config, self.tokenizer_manager)
            for chunk in chunks:
                if chunk and chunk.strip():
                    yield chunk.strip()
    
    def _create_chunk_dict(self, doc: Dict[str, Any], chunk: str, idx: int, total: int) -> Dict[str, Any]:
        """Create enhanced chunk dictionary with additional metadata."""
        # Explicitly ensure chunk is a string
        if isinstance(chunk, list):
            chunk = " ".join(str(x) for x in chunk)
        elif not isinstance(chunk, str):
            chunk = str(chunk)
        
        # Calculate token count for the chunk
        token_count = 0
        if self.config.use_tokens:
            token_count = self.tokenizer_manager.count_tokens(chunk, self.config.tokenizer_name)
        
        
        # HR-SPECIFIC METADATA EXTRACTION (heuristic based)
        employee_categories = []
        compliance_flags = []
        effective_dates = []

        if any(k in chunk.lower() for k in ["contract", "temporary", "permanent", "full-time", "part-time"]):
            employee_categories.append("general_workforce")
        if "posh" in chunk.lower() or "prevention of sexual harassment" in chunk.lower():
            compliance_flags.append("PoSH")
        if "gdpr" in chunk.lower():
            compliance_flags.append("GDPR")
        effective_dates += re.findall(r'\b(?:effective from|start date|as of)\s+\d{1,2}[-/\s]\w+[-/\s]\d{2,4}\b', chunk, flags=re.IGNORECASE)

        # DOCUMENT RELATIONSHIP MAPPING PLACEHOLDER
        cross_references = re.findall(r'(see section\s+[\dIVX]+)', chunk, flags=re.IGNORECASE)
        hierarchy_level = chunk.count("\n#")  # Rough proxy for heading depth

        return {
            "title": f"{doc.get('title', 'Untitled')} - Part {idx + 1}",
            "text": chunk,
            "content": chunk,
            "source_file": doc.get("source_file", "Unknown"),
            "chunk_index": idx,
            "total_chunks": total,
            "chunk_size": len(chunk),
            "token_count": token_count,
            "original_title": doc.get("title", "Untitled"),
            "metadata": {
                **doc.get("metadata", {}),
                "chunking_strategy": getattr(self, '_last_strategy_used', 'unknown'),
                "tokenizer": self.config.tokenizer_name if self.config.use_tokens else None,
                "chunk_created_at": time.time(),
                "language": self.config.language,
                "word_count": len(chunk.split()),
                "sentence_count": len(self._split_into_sentences(chunk))
            }
        }
    
    def _create_chunk_dicts_from_cache(self, document: Dict[str, Any], cache_key: str) -> List[Dict[str, Any]]:
        """Create chunk dictionaries from cached chunks."""
        cached_chunks = self._chunk_cache[cache_key]
        return [self._create_chunk_dict(document, chunk, i, len(cached_chunks)) 
                for i, chunk in enumerate(cached_chunks)]
    
    def _normalize_text(self, text: str) -> str:
        """Enhanced text normalization with multilingual support."""
        # Unicode normalization
        text = unicodedata.normalize("NFKC", text)
        
        # Replace various Unicode spaces and control characters
        replacements = {
            "\u00A0": " ",      # Non-breaking space
            "\u2028": "\n",     # Line separator
            "\u2029": "\n\n",   # Paragraph separator
            "\u200B": "",      # Zero-width space
            "\u200C": "",      # Zero-width non-joiner
            "\u200D": "",      # Zero-width joiner
            "\uFEFF": "",      # Byte order mark
            "\u2060": "",      # Word joiner
            "\u00AD": "",      # Soft hyphen
        }
        
        for old, new in replacements.items():
            text = text.replace(old, new)
        
        # Normalize line endings
        text = re.sub(r'\r\n?', '\n', text)
        
        # Reduce multiple consecutive newlines
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # Normalize whitespace (but preserve intentional formatting if configured)
        if not self.config.preserve_formatting:
            text = re.sub(r'[ \t]+', ' ', text)
            text = re.sub(r' *\n *', '\n', text)
        else:
            # More conservative whitespace normalization
            text = re.sub(r'[ \t]{2,}', ' ', text)
        
        # Handle special characters based on language
        if self.config.language != "en":
            text = self._normalize_multilingual(text)
        
        return text.strip()
    
    def _normalize_multilingual(self, text: str) -> str:
        """Apply language-specific normalization."""
        # This can be extended for specific language requirements
        if self.config.language.startswith("zh"):  # Chinese
            # Handle Chinese punctuation and spacing
            text = re.sub(r'([。！？])\s+', r'\1', text)
        elif self.config.language.startswith("ja"):  # Japanese
            # Handle Japanese punctuation
            text = re.sub(r'([。！？])\s+', r'\1', text)
        elif self.config.language.startswith("ar"):  # Arabic
            # Handle RTL text normalization
            text = re.sub(r'\u200F|\u200E', '', text)  # Remove direction marks
        
        return text
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Enhanced sentence splitting with multilingual support."""
        if SPACY_AVAILABLE and self.config.language in ["en", "auto"]:
            try:
                import spacy
                nlp = spacy.load(SPACY_MODEL_PATH)
                doc = nlp(text)
                return [sent.text.strip() for sent in doc.sents if sent.text.strip()]
            except Exception as e:
                logger.warning(f"Spacy sentence splitting failed: {e}")
        
        # Fallback to enhanced regex-based splitting
        # Extended abbreviations list
        abbreviations = {
            'Dr', 'Mr', 'Mrs', 'Ms', 'Prof', 'Sr', 'Jr', 'Inc', 'Ltd', 'Corp',
            'Co', 'vs', 'etc', 'i.e', 'e.g', 'cf', 'al', 'St', 'Ave', 'Blvd',
            'Fig', 'Vol', 'No', 'pp', 'ch', 'sec', 'dept', 'univ', 'govt',
            'admin', 'assoc', 'bros', 'conf', 'corp', 'dept', 'est', 'exec'
        }
        
        # Create pattern for abbreviations
        abbrev_pattern = r'\b(?:' + '|'.join(re.escape(a) for a in abbreviations) + r')\.'
        
        # Temporarily replace abbreviations
        temp_text = re.sub(abbrev_pattern, lambda m: m.group().replace('.', '<DOT>'), text)
        
        # Enhanced sentence boundary detection
        # Handle various punctuation patterns
        sentence_endings = r'(?<=[.!?])\s+(?=[A-Z"\(\[])'
        sentences = re.split(sentence_endings, temp_text)
        
        # Restore abbreviations and clean up
        cleaned_sentences = []
        for s in sentences:
            s = s.replace('<DOT>', '.').strip()
            if s and len(s) > 3:  # Filter out very short fragments
                cleaned_sentences.append(s)
        
        return cleaned_sentences
    
    def _update_metrics(self, start_time: float, chunks: List[str], strategy_name: str):
        """Update processing metrics."""
        processing_time = time.time() - start_time
        
        self.metrics.total_chunks = len(chunks)
        self.metrics.total_characters = sum(len(chunk) for chunk in chunks)
        self.metrics.processing_time = processing_time
        self.metrics.strategy_used = strategy_name
        self.metrics.average_chunk_size = (
            self.metrics.total_characters / len(chunks) if chunks else 0
        )
        
        # Calculate token metrics if using tokens
        if self.config.use_tokens:
            total_tokens = sum(
                self.tokenizer_manager.count_tokens(chunk, self.config.tokenizer_name) 
                for chunk in chunks
            )
            self.metrics.total_tokens = total_tokens
            self.metrics.token_efficiency = (
                total_tokens / (self.config.max_tokens * len(chunks)) if chunks else 0
            )
        
        # Update global stats
        self._processing_stats["total_documents"] += 1
        self._processing_stats["total_time"] += processing_time
        self._last_strategy_used = strategy_name
        
        logger.info(f"Processed document: {len(chunks)} chunks, "
                    f"{processing_time:.2f}s, strategy: {strategy_name}")
    
    def validate_chunks(self, chunks: List[str]) -> Dict[str, Any]:
        """Enhanced chunk validation with comprehensive metrics."""
        if not chunks:
            return {
                "valid": False,
                "count": 0,
                "avg_length": 0,
                "min_length": 0,
                "max_length": 0,
                "total_length": 0,
                "issues": ["No chunks provided"],
                "token_stats": {},
                "quality_score": 0.0
            }
        
        lengths = [len(c) for c in chunks]
        token_counts = []
        
        # Calculate token statistics if enabled
        if self.config.use_tokens:
            token_counts = [
                self.tokenizer_manager.count_tokens(chunk, self.config.tokenizer_name) 
                for chunk in chunks
            ]
        
        # Identify issues
        issues = []
        for i, chunk in enumerate(chunks):
            chunk_len = len(chunk)
            
            # Size validation
            if chunk_len < self.config.min_chunk_size:
                issues.append(f"Chunk {i} too short: {chunk_len} chars")
            elif chunk_len > self.config.max_chunk_size:
                issues.append(f"Chunk {i} too long: {chunk_len} chars")
            
            # Content validation
            if not chunk.strip():
                issues.append(f"Chunk {i} is empty or whitespace only")
            
            # Token validation
            if self.config.use_tokens and token_counts:
                token_count = token_counts[i]
                if token_count > self.config.max_tokens:
                    issues.append(f"Chunk {i} exceeds token limit: {token_count} tokens")
        
        # Calculate quality score
        valid_chunks = len(chunks) - len([i for i in issues if "too short" in i or "empty" in i])
        quality_score = valid_chunks / len(chunks) if chunks else 0.0
        
        # Prepare token statistics
        token_stats = {}
        if token_counts:
            token_stats = {
                "total_tokens": sum(token_counts),
                "avg_tokens": sum(token_counts) / len(token_counts),
                "min_tokens": min(token_counts),
                "max_tokens": max(token_counts),
                "token_efficiency": sum(token_counts) / (self.config.max_tokens * len(chunks))
            }
        
        return {
            "valid": len(issues) == 0,
            "count": len(chunks),
            "avg_length": sum(lengths) / len(lengths),
            "min_length": min(lengths),
            "max_length": max(lengths),
            "total_length": sum(lengths),
            "issues": issues,
            "token_stats": token_stats,
            "quality_score": quality_score,
            "chunk_size_distribution": self._calculate_size_distribution(lengths),
            "overlap_analysis": self._analyze_overlap(chunks) if len(chunks) > 1 else {}
        }
    
    def _calculate_size_distribution(self, lengths: List[int]) -> Dict[str, int]:
        """Calculate chunk size distribution."""
        if not lengths:
            return {}
        
        min_len, max_len = min(lengths), max(lengths)
        range_size = (max_len - min_len) / 5 if max_len > min_len else 1
        
        distribution = {
            "very_small": 0,  # < 20th percentile
            "small": 0,       # 20-40th percentile
            "medium": 0,      # 40-60th percentile
            "large": 0,       # 60-80th percentile
            "very_large": 0   # > 80th percentile
        }
        
        sorted_lengths = sorted(lengths)
        p20 = sorted_lengths[len(sorted_lengths) // 5]
        p40 = sorted_lengths[2 * len(sorted_lengths) // 5]
        p60 = sorted_lengths[3 * len(sorted_lengths) // 5]
        p80 = sorted_lengths[4 * len(sorted_lengths) // 5]
        
        for length in lengths:
            if length < p20:
                distribution["very_small"] += 1
            elif length < p40:
                distribution["small"] += 1
            elif length < p60:
                distribution["medium"] += 1
            elif length < p80:
                distribution["large"] += 1
            else:
                distribution["very_large"] += 1
        
        return distribution
    
    def _analyze_overlap(self, chunks: List[str]) -> Dict[str, Any]:
        """Analyze overlap between consecutive chunks."""
        if len(chunks) < 2:
            return {}
        
        overlaps = []
        for i in range(1, len(chunks)):
            overlap = self._calculate_chunk_overlap(chunks[i-1], chunks[i])
            overlaps.append(overlap)
        
        return {
            "avg_overlap_ratio": sum(overlaps) / len(overlaps),
            "min_overlap_ratio": min(overlaps),
            "max_overlap_ratio": max(overlaps),
            "total_overlaps": len(overlaps)
        }
    
    def _calculate_chunk_overlap(self, chunk1: str, chunk2: str) -> float:
        """Calculate overlap ratio between two chunks."""
        words1 = set(chunk1.lower().split())
        words2 = set(chunk2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get comprehensive processing metrics."""
        return {
            "current_session": {
                "total_chunks": self.metrics.total_chunks,
                "total_tokens": self.metrics.total_tokens,
                "total_characters": self.metrics.total_characters,
                "processing_time": self.metrics.processing_time,
                "average_chunk_size": self.metrics.average_chunk_size,
                "token_efficiency": self.metrics.token_efficiency,
                "strategy_used": self.metrics.strategy_used,
                "errors": len(self.metrics.errors),
                "warnings": len(self.metrics.warnings)
            },
            "global_stats": self._processing_stats,
            "configuration": {
                "chunk_size": self.config.chunk_size,
                "max_tokens": self.config.max_tokens,
                "strategy": self.config.strategy,
                "tokenizer": self.config.tokenizer_name,
                "use_tokens": self.config.use_tokens,
                "language": self.config.language
            },
            "cache_stats": {
                "cached_documents": len(self._chunk_cache),
                "cache_enabled": self.config.cache_tokenization
            }
        }
    
    def clear_cache(self):
        """Clear tokenization and chunk cache."""
        self._chunk_cache.clear()
        self.tokenizer_manager._cache.clear()
        logger.info("Caches cleared")
    
    def __del__(self):
        """Cleanup resources."""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)
    
    def log_retrieval_performance(self, score: float, chunk_size: int, chunk_overlap: int):
        """Stub: Log retrieval performance for future tuning."""
        # TODO: Implement actual logging to file/db/monitoring
        pass