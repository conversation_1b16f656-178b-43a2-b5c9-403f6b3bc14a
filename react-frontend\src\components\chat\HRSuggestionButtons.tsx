import React from 'react';
import { motion } from 'framer-motion';
import { 
  Calendar, 
  FileText, 
  Users, 
  Home, 
  Briefcase, 
  Clock
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface HRSuggestion {
  title: string;
  description: string;
  query: string;
  icon: React.ReactNode;
  color: string;
}

interface HRSuggestionButtonsProps {
  onSuggestionClick: (query: string) => void;
  className?: string;
}

const HRSuggestionButtons: React.FC<HRSuggestionButtonsProps> = ({
  onSuggestionClick,
  className
}) => {
  const suggestions: HRSuggestion[] = [
    {
      title: "Leave Policy",
      description: "Learn about vacation, sick leave, and time-off policies",
      query: "What is the company's leave policy and how do I request time off?",
      icon: <Calendar className="w-6 h-6" />,
      color: "bg-blue-500"
    },
    {
      title: "Referral Program", 
      description: "Understand how employee referrals work and rewards",
      query: "How does the employee referral program work and what are the benefits?",
      icon: <Users className="w-6 h-6" />,
      color: "bg-green-500"
    },
    {
      title: "Dress Code",
      description: "Check the company dress code and appearance guidelines",
      query: "What is the company dress code policy?",
      icon: <FileText className="w-6 h-6" />,
      color: "bg-purple-500"
    },
    {
      title: "Work from Home",
      description: "Remote work policies and hybrid work arrangements",
      query: "Tell me about the work from home and remote work policies",
      icon: <Home className="w-6 h-6" />,
      color: "bg-orange-500"
    },
    {
      title: "Benefits",
      description: "Explore health insurance, retirement, and other benefits",
      query: "What are the company benefits including health insurance and retirement plans?",
      icon: <Briefcase className="w-6 h-6" />,
      color: "bg-indigo-500"
    },
    {
      title: "Request Time Off",
      description: "Submit vacation requests and check your balance",
      query: "How do I request time off and check my vacation balance?",
      icon: <Clock className="w-6 h-6" />,
      color: "bg-red-500"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.9 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    hover: {
      y: -5,
      scale: 1.02,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 10
      }
    },
    tap: {
      scale: 0.98
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn("grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-w-4xl mx-auto", className)} // gap-3, max-w-4xl
    >
      {suggestions.map((suggestion, index) => (
        <motion.button
          key={index}
          variants={cardVariants}
          whileHover="hover"
          whileTap="tap"
          onClick={() => onSuggestionClick(suggestion.query)}
          className={cn(
            // Reduced width, padding, font, rounded, and shadow for compact size
            "w-60 py-3 px-3 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700",
            "shadow-sm hover:shadow-md transition-shadow duration-200",
            "text-left focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
            "group text-sm", // text-base -> text-sm
            className
          )}
        >
          <div className="flex items-start gap-2"> {/* gap reduced from 3 to 2 */}
            <div className={cn(
              "p-2 rounded-md text-white flex-shrink-0", // p-2.5 -> p-2, rounded-lg -> rounded-md
              suggestion.color,
              "group-hover:scale-105 transition-transform duration-200"
            )}>
              {/* icon size reduced */}
              {React.cloneElement(suggestion.icon as React.ReactElement, { className: "w-5 h-5" })}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-1 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors text-sm"> {/* mb-1.5 -> mb-1, text-base -> text-sm */}
                {suggestion.title}
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 leading-snug"> {/* text-sm -> text-xs, leading-relaxed -> leading-snug */}
                {suggestion.description}
              </p>
            </div>
          </div>
        </motion.button>
      ))}
    </motion.div>
  );
};

export default HRSuggestionButtons;
