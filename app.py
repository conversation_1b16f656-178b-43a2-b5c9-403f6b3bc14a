"""
Main application entry point for the Advanced HR Assistant Chatbot.
Optimized for performance with local document processing and vector search.
"""
import os
import time
import asyncio
from pathlib import Path
from dotenv import load_dotenv
import multiprocessing
from src.monitoring import initialize_apm
# from admin_dashboard.override_manager import get_override
# from admin_dashboard.escalation.live_escalation import escalate_to_slack
# from admin_dashboard.twofa_manager import generate_secret, get_qr_code, is_2fa_enabled
import base64
import pyotp
import qrcode
from io import BytesIO
import secrets
import signal
import sys
from src.intent.intent_classifier import IntentClassifier
from functools import wraps
from flask import Flask, request, jsonify, session, send_from_directory
from flask_cors import CORS
from markdown import markdown
from src.utils.logger import get_logger
from src.chain.chain_builder import ChainBuilder
from src.conversation.history_manager import HistoryManager
from src.speech.speech_to_text import SpeechToText
from src.speech.text_to_speech import TextToSpeech
from src.document_processing.training_pipeline import Training<PERSON>ipeline
from src.user_authentication.user_authorisation import AuthService
from src.utils.email_service import EmailService
from src.config import HR_EMAILS, ENABLE_EMAIL_ESCALATION, GROQ_API_KEY, MOCKAPI_URL
import requests
from src.database.user_db import UserModel
from src.database.session_db import SessionModel
from src.database.user_db import ConversationModel
from collections import Counter, defaultdict
from datetime import datetime, timedelta
from src.document_processing.file_processor import FileProcessor
from src.ner.entity_extractor import EntityExtractor
print("DEBUG: FileProcessor import successful:", FileProcessor)

# Force exit on Ctrl+C

def handle_sigint(sig, frame):
    print("Received shutdown signal, exiting now.")
    sys.exit(0)

signal.signal(signal.SIGINT, handle_sigint)

# Must be at the very top for Windows multiprocessing
multiprocessing.set_start_method('spawn', force=True)

# Load environment variables from .env file
load_dotenv()

# Validate required environment variables
if not os.getenv("GROQ_API_KEY"):
    raise ValueError("GROQ_API_KEY is not set in the environment or .env file")

if not os.getenv("FLASK_SECRET_KEY"):
    raise ValueError("FLASK_SECRET_KEY is not set in the environment or .env file. Please set a strong, random key.")

# Import all other modules
from flask import Flask, request, jsonify, session, send_from_directory
from flask_cors import CORS
from markdown import markdown
from src.utils.logger import get_logger
from src.chain.chain_builder import ChainBuilder
from src.conversation.history_manager import HistoryManager
from src.speech.speech_to_text import SpeechToText
from src.speech.text_to_speech import TextToSpeech
from src.document_processing.training_pipeline import TrainingPipeline
from src.user_authentication.user_authorisation import AuthService
from src.utils.email_service import EmailService
from src.config import HR_EMAILS, ENABLE_EMAIL_ESCALATION, GROQ_API_KEY, MOCKAPI_URL
import requests
from src.database.user_db import UserModel
from src.database.session_db import SessionModel
from src.database.user_db import ConversationModel
from collections import Counter, defaultdict
from datetime import datetime, timedelta

# Initialize logger
logger = get_logger(__name__)

# Global variables for lazy initialization
_chain_builder = None
_history_manager = None
_speech_to_text = None
_text_to_speech = None
_auth_service = None
_email_service = None
_session_history = {}
app_state = {}
_user_model = None  # <-- Add this line

# Initialize APM/metrics at startup
apm = initialize_apm()

# --- Move get_user_model to module level and enforce singleton ---
def get_user_model():
    global _user_model
    if _user_model is None:
        from src.database.user_db import UserModel
        _user_model = UserModel()
    return _user_model

def get_chain_builder():
    global _chain_builder
    if _chain_builder is None:
        _chain_builder = ChainBuilder()
    return _chain_builder

def get_history_manager():
    global _history_manager
    if _history_manager is None:
        _history_manager = HistoryManager()
    return _history_manager

def get_speech_to_text():
    global _speech_to_text
    if _speech_to_text is None:
        _speech_to_text = SpeechToText()
    return _speech_to_text

def get_text_to_speech():
    global _text_to_speech
    if _text_to_speech is None:
        _text_to_speech = TextToSpeech()
    return _text_to_speech

def get_auth_service():
    global _auth_service
    if _auth_service is None:
        _auth_service = AuthService()
        # Patch the AuthService instance to use the singleton UserModel
        _auth_service.user_model = get_user_model()
    return _auth_service

def get_email_service():
    global _email_service
    if _email_service is None:
        _email_service = EmailService()
    return _email_service

def run_async_in_sync(coro):
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, coro)
                return future.result()
        else:
            return loop.run_until_complete(coro)
    except RuntimeError:
        return asyncio.run(coro)

def initialize_services():
    if os.environ.get("WERKZEUG_RUN_MAIN") == "true":
        logger.info("Initializing core services...")

        get_chain_builder()
        get_history_manager()

        if ENABLE_EMAIL_ESCALATION and HR_EMAILS:
            get_email_service()

        try:
            pipeline = TrainingPipeline()
            app_state["pipeline"] = pipeline

            # HR file ingestion removed from app startup for production best practices.

        except AttributeError as e:
            logger.warning("🔁 process_hr_files() not found in TrainingPipeline", extra={"error": str(e)})
        except Exception as e:
            import traceback
            error_msg = f"Error during HR document ingestion: {e}\n{traceback.format_exc()}"
            print(error_msg)
            logger.error("Error during HR document ingestion", extra={
                "error": str(e),
                "traceback": traceback.format_exc()
            })


def create_app():
    """Create and configure the Flask application."""
    app = Flask(__name__, static_folder="react-frontend/dist")
    app.config['TEMPLATES_AUTO_RELOAD'] = True
    app.jinja_env.cache = {}

    # --- CORS SETTINGS FOR CROSS-ORIGIN 2FA LOGIN ---
    # For local development, use Lax/False. For production HTTPS, use None/True.
    app.config['SESSION_COOKIE_SAMESITE'] = 'None'  # Use 'None' for cross-origin cookies (must be HTTPS)
    app.config['SESSION_COOKIE_SECURE'] = True      # True for HTTPS, False for local dev
    app.config['SESSION_COOKIE_HTTPONLY'] = True

    # Set a secret key for session management
    app.secret_key = os.environ.get('FLASK_SECRET_KEY', os.urandom(24))

    # --- CORS: Allow credentials and specify allowed origins ---
    from flask_cors import CORS
    CORS(app, origins=["http://localhost:3000", "http://127.0.0.1:3000"], supports_credentials=True, allow_headers=["*"], methods=["GET", "POST", "OPTIONS", "PUT", "DELETE"])

    # --- API ROUTES (use /api/ prefix) ---
    @app.route("/api/query", methods=["POST"])
    def query():
        """Process a text query with proper document verification."""
        try:
            data = request.json
            user_query = data["query"]
            device_id = data["device_id"]
            files_info = data.get("files_info", [])

            if files_info:
                pipeline = TrainingPipeline()
                for file_info in files_info:
                    if not pipeline.is_file_processed(file_info.get("name")):
                        logger.warning(f"File not processed yet — marker not found for {file_info.get('name')}")
                        return jsonify({
                            "error": f"File {file_info.get('name')} not processed yet — marker not found. Please wait for processing to complete.",
                            "success": False
                        }), 400

            query_start_time = time.time()
            chain_builder = get_chain_builder()

            doc_count = chain_builder.get_vector_database_count()
            if doc_count == 0:
                logger.warning("No documents in vector database")

            # --- LEAVE BALANCE QUERY HANDLING ---
            if any(kw in user_query.lower() for kw in ["leave balance", "how many leaves", "my leave", "leaves in my account"]):
                email = data.get("email")
                employee_id = data.get("employee_id")
                if not email or not employee_id:
                    return jsonify({
                        "error": "Email and employee ID are required for leave balance queries.",
                        "success": False
                    }), 400
                # Call the leave balance logic directly
                # Simulate a request context for get_leave_balance
                with app.test_request_context(f"/api/leave-balance?email={email}&employee_id={employee_id}"):
                    return get_leave_balance()
            # --- END LEAVE BALANCE HANDLING ---

            if hasattr(chain_builder, 'run_chain_sync'):
                result = chain_builder.run_chain_sync(user_query, device_id, files_info=files_info)
            else:
                result = run_async_in_sync(chain_builder.run_chain(user_query, device_id, files_info=files_info))

            if isinstance(result, dict) and result.get("sources"):
                logger.info(f"Query used {len(result['sources'])} document sources")
            else:
                logger.warning("Query result contains no document sources - RAG may not be working")

            response_content = result.get("content", "") if isinstance(result, dict) else ""

            # Escalate to Slack if response is empty or unhelpful
            # if not response_content.strip() or response_content.strip().startswith("I'm sorry"):
            #     escalate_to_slack(user_query, device_id)

            if not response_content.strip():
                response_content = "I'm sorry, I couldn't find relevant information in the available documents. Please ensure your documents are properly uploaded and processed."

            # Save conversation to database
            from src.database.conversation_store import ConversationStore
            ConversationStore().save_conversation(
                chat_id=data.get('chat_id'),
                user_query=user_query,
                assistant_response=response_content,
                language=result.get("language", "en") if isinstance(result, dict) else "en",
                device_id=device_id,
                query_start_time=query_start_time,
                response_end_time=time.time(),
                intent=(result.get("intent") if isinstance(result, dict) and result.get("intent") else "unknown")
            )

            final_result = {
                "response": markdown(response_content),
                "sources": result.get("sources", []) if isinstance(result, dict) else [],
                "language": result.get("language", "en") if isinstance(result, dict) else "en",
                "response_time": time.time() - query_start_time,
                "document_count": doc_count
            }

            return jsonify(final_result)

        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return jsonify({
                "error": "Query processing failed",
                "message": "Please check if documents are properly uploaded and try again."
            }), 500

    @app.route("/api/speech-to-text", methods=["POST"])
    def speech_to_text_api():
        """Convert speech to text."""
        try:
            audio_data = request.files.get("audio")

            if audio_data:
                temp_path = Path(app.root_path) / "temp_audio.wav"
                audio_data.save(temp_path)
                text = "Audio file processing not implemented yet"
            else:
                text = get_speech_to_text().recognize_speech()

            return jsonify({"text": text})

        except Exception as e:
            logger.error(f"Error in speech-to-text: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/upload-document", methods=["POST"])
    def upload_document():
        """Upload a document (no training)."""
        try:
            if "file" not in request.files:
                return jsonify({"error": "No file uploaded", "success": False}), 400

            file = request.files["file"]
            if file.filename == "":
                return jsonify({"error": "No file selected", "success": False}), 400

            raw_dir = Path(app.root_path) / "data" / "raw"
            os.makedirs(raw_dir, exist_ok=True)
            file_path = raw_dir / file.filename
            file.save(file_path)

            return jsonify({
                "success": True,
                "filename": file.filename,
                "uploaded": True
            })

        except Exception as e:
            logger.error(f"Error uploading document: {e}")
            return jsonify({"error": str(e), "success": False}), 500

    @app.route("/api/start-training", methods=["POST"])
    def start_training():
        """Explicitly start training on a file."""
        try:
            data = request.json
            filename = data.get("filename")
            if not filename:
                return jsonify({"error": "No filename provided", "success": False}), 400

            raw_dir = Path(app.root_path) / "data" / "raw"
            file_path = raw_dir / filename
            if not file_path.exists():
                return jsonify({"error": "File not found", "success": False}), 404

            pipeline = TrainingPipeline()
            success = pipeline.process_file(file_path, force_reprocess=False)

            if not success:
                return jsonify({
                    "error": "Document processing failed",
                    "success": False
                }), 500

            return jsonify({
                "success": True,
                "filename": filename,
                "trained": True
            })

        except Exception as e:
            logger.error(f"Error starting training: {e}")
            return jsonify({"error": str(e), "success": False}), 500

    @app.route("/api/summarize-document", methods=["POST"])
    def summarize_document():
        """Upload a document, process it fully, and return a summary."""
        try:
            if "file" not in request.files:
                return jsonify({"error": "No file uploaded", "success": False}), 400

            file = request.files["file"]
            if file.filename == "":
                return jsonify({"error": "No file selected", "success": False}), 400

            # Save to data/raw/ for full pipeline processing
            raw_dir = Path(app.root_path) / "data" / "raw"
            os.makedirs(raw_dir, exist_ok=True)
            file_path = raw_dir / file.filename
            file.save(file_path)

            # Process the file fully (chunks, embeddings, marker)
            pipeline = TrainingPipeline()
            pipeline.process_file(file_path, force_reprocess=True)

            # Check for marker file
            processed_dir = Path(app.root_path) / "data" / "processed"
            marker_file = processed_dir / f"{file.filename}.processed_raw.json"
            ready_for_query = marker_file.exists()

            # Extract text for summary
            document = FileProcessor.process_file(file_path)
            content = document.get("content", "")

            # Generate summary using your LLM/chain
            chain_builder = get_chain_builder()
            summary = chain_builder.summarize(content)  # You may need to implement this method

            return jsonify({
                "success": True,
                "summary": summary,
                "filename": file.filename,
                "status": "ready_for_query" if ready_for_query else "processing"
            })

        except Exception as e:
            logger.error(f"Error summarizing document: {e}")
            return jsonify({"error": str(e), "success": False}), 500

    @app.route("/api/clear-history", methods=["POST"])
    def clear_history():
        """Clear conversation history for a device."""
        try:
            data = request.json
            device_id = data["device_id"]

            history_manager = get_history_manager()
            history_manager.clear_history(device_id)

            if device_id in _session_history:
                del _session_history[device_id]

            return jsonify({"success": True})

        except Exception as e:
            logger.error(f"Error clearing history: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/confirm-escalation", methods=["POST"])
    def confirm_escalation():
        """Confirm escalation and send email to HR."""
        try:
            data = request.json
            user_query = data["query"]
            device_id = data["device_id"]

            history = get_history_manager().get_history(device_id)

            if not ENABLE_EMAIL_ESCALATION:
                return jsonify({
                    "success": False,
                    "message": "Email escalation is not enabled"
                }), 400

            if not HR_EMAILS:
                return jsonify({
                    "success": False,
                    "message": "No HR email addresses configured"
                }), 400

            result = get_email_service().send_escalation_email(
                hr_emails=HR_EMAILS,
                user_query=user_query,
                conversation_history=history
            )

            if result["success"]:
                return jsonify({
                    "success": True,
                    "message": "Your question has been escalated to the HR team. They will follow up with you directly."
                })
            else:
                logger.error(f"Error sending escalation email: {result['message']}")
                return jsonify({
                    "success": False,
                    "message": "There was an error escalating your question. Please try again later."
                }), 500

        except Exception as e:
            logger.error(f"Error confirming escalation: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/file-preview", methods=["GET"])
    def file_preview():
        """Get file content for preview."""
        try:
            filename = request.args.get("filename")
            if not filename:
                return jsonify({"error": "No filename provided"}), 400

            raw_dir = Path(app.root_path) / "data" / "raw"
            file_path = raw_dir / filename

            if not file_path.exists():
                return jsonify({"error": f"File {filename} not found"}), 404

            file_extension = file_path.suffix.lower()

            if file_extension in ['.txt', '.md']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return jsonify({
                    "success": True,
                    "content": content,
                    "content_type": "text"
                })
            elif file_extension in ['.pdf', '.docx']:
                return jsonify({
                    "success": True,
                    "file_path": str(file_path),
                    "content_type": file_extension[1:]
                })
            else:
                return jsonify({
                    "error": f"Unsupported file type: {file_extension}"
                }), 400

        except Exception as e:
            logger.error(f"Error getting file preview: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/health/documents", methods=["GET"])
    def document_health_check():
        """Check document processing and vector database health."""
        try:
            pipeline = TrainingPipeline()
            chain_builder = get_chain_builder()

            raw_files = len(list(Path(app.root_path).glob("data/raw/*")))
            processed_files = len(list(Path(app.root_path).glob("data/processed/*")))
            vector_count = chain_builder.get_vector_database_count() if hasattr(chain_builder, 'get_vector_database_count') else 0

            processing_healthy = processed_files > 0 and vector_count > 0

            return jsonify({
                "success": True,
                "raw_files": raw_files,
                "processed_files": processed_files,
                "vector_embeddings": vector_count,
                "processing_healthy": processing_healthy,
                "recommendations": [
                    "Upload documents if none are processed",
                    "Check document processing logs if embeddings are 0",
                    "Restart application if processing appears stuck"
                ] if not processing_healthy else []
            })

        except Exception as e:
            logger.error(f"Document health check failed: {e}")
            return jsonify({
                "success": False,
                "error": str(e)
            }), 500

    @app.route("/api/process-hr-files", methods=["POST"])
    def process_hr_files():
        """Process HR files from a directory."""
        try:
            data = request.json
            directory = data.get("directory", "Hr Files")
            force_reprocess = data.get("force_reprocess", False)

            pipeline = TrainingPipeline()
            num_processed = pipeline.process_hr_files(Path(directory), force_reprocess=force_reprocess)

            return jsonify({
                "success": True,
                "files_processed": num_processed,
                "force_reprocess": force_reprocess
            })

        except Exception as e:
            logger.error(f"Error processing HR files: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/register", methods=["POST", "OPTIONS"])
    def register():
        """Register a new user."""
        try:
            if request.method == "OPTIONS":
                return ('', 204)
            data = request.json
            email = data.get("email")
            password = data.get("password")
            full_name = data.get("full_name")
            employee_id = data.get("employee_id")

            if not all([email, password, full_name]):
                return jsonify({
                    "success": False,
                    "message": "Email, password, and full name are required"
                }), 400

            # Generate both secrets for new user
            auth_service = get_auth_service()
            user_2fa_secret = auth_service.generate_2fa_secret()
            admin_2fa_secret = auth_service.generate_2fa_secret()

            try:
                password_hash = auth_service.hash_password(password)
            except ValueError as ve:
                return jsonify({"success": False, "message": str(ve)}), 400

            result = auth_service.user_model.create_user(
                email=email,
                password_hash=password_hash,
                full_name=full_name,
                employee_id=employee_id,
                two_fa_secret_user=user_2fa_secret,
                two_fa_secret_admin=admin_2fa_secret,
                role='user'
            )

            if not result.get('success'):
                return jsonify({"success": False, "message": result.get('message', 'Failed to register user')}), 400

            # Return both QR URLs for setup (optional, for demo)
            user_qr = auth_service.get_2fa_qr_url(email, user_2fa_secret, role='user')
            admin_qr = auth_service.get_2fa_qr_url(email, admin_2fa_secret, role='admin')
            return jsonify({
                "success": True,
                "message": "User registered successfully",
                "2fa_qr_url_user": user_qr,
                "2fa_qr_url_admin": admin_qr
            })
        except Exception as e:
            import traceback
            print(traceback.format_exc())
            return jsonify({"success": False, "message": f"An error occurred: {str(e)}"}), 500

    @app.route('/api/user-2fa-setup', methods=["POST"])
    def user_2fa_setup():
        data = request.json
        email = data.get("email")
        if not email:
            return jsonify({"success": False, "message": "Email is required"}), 400
        user = get_user_model().get_user_by_email(email)
        if not user:
            return jsonify({"success": False, "message": "User not found"}), 404
        secret = user.get("two_fa_secret_user")
        if not secret:
            # Optionally generate and save a new secret here
            secret = get_auth_service().generate_2fa_secret()
            # You could update the user in DB here if needed
        qr_url = get_auth_service().get_2fa_qr_url(email, secret, role='user')

        # --- Generate QR code image as data URL ---
        qr_img = qrcode.make(qr_url)
        buffered = BytesIO()
        qr_img.save(buffered, format="PNG")
        img_str = base64.b64encode(buffered.getvalue()).decode("utf-8")
        data_url = f"data:image/png;base64,{img_str}"

        return jsonify({"success": True, "qr_url": data_url})

    @app.route('/api/auth/verify-2fa', methods=["POST"])
    def verify_2fa():
        """Verify 2FA code for pending login."""
        try:
            data = request.json
            code = data.get("code")

            if not code:
                return jsonify({"success": False, "message": "2FA code is required"}), 400

            # Get pending login from session
            user_id = session.get("pending_user_id")
            if not user_id:
                return jsonify({"success": False, "message": "No pending login found"}), 400

            user = get_auth_service().user_model.get_user_by_id(user_id)
            if not user:
                return jsonify({"success": False, "message": "User not found"}), 404

            # Determine role based on user object
            user_role = user.get('role', 'user')

            # Verify 2FA code
            if not get_auth_service().verify_2fa_code(user, code, role=user_role):
                return jsonify({"success": False, "message": "Invalid 2FA code"}), 401

            # 2FA verification successful - complete the login
            get_auth_service().user_model.update_last_login(user['id'])
            token = get_auth_service().generate_token(user)

            # Set session data
            session["user_id"] = user["id"]
            session["email"] = user["email"]
            session.pop("pending_user_id", None)  # Clear pending login
            logger.info(f"pending_user_id cleared from session for user {user['id']}.")

            # Extract request data at the top
            ip_address = request.remote_addr
            user_agent = request.headers.get('User-Agent')

            # Create session record
            try:
                from src.database.session_db import SessionModel
                import uuid
                from datetime import datetime
                session_model = SessionModel()
                session_model.create_session(
                    id=str(uuid.uuid4()),
                    user_id=user['id'],
                    ip_address=ip_address,
                    user_agent=user_agent,
                    browser=None,  # Optionally parse from user_agent
                    os=None,       # Optionally parse from user_agent
                    device_fingerprint=None,
                    login_time=datetime.utcnow().isoformat(),
                    last_activity=datetime.utcnow().isoformat(),
                    location_country=None,
                    location_city=None,
                    latitude=None,
                    longitude=None,
                    auth_method="password_2fa",
                    success=True,
                    user_type="chatbot_user"
                )
            except Exception as e:
                logger.exception("Failed to create chatbot user session record")

            return jsonify({
                "success": True,
                "message": "2FA verification successful",
                "token": token,
                "user": {
                    "id": user['id'],
                    "email": user['email'],
                    "full_name": user.get('full_name', ''),
                    "employee_id": user.get('employee_id', ''),
                }
            }), 200

        except Exception as e:
            logger.error(f"Error verifying 2FA: {e}")
            return jsonify({
                "success": False,
                "message": f"An error occurred: {str(e)}"
            }), 500

    @app.route("/api/user", methods=["GET"])
    def get_user():
        """Get the current user's information."""
        try:
            user_id = session.get("user_id")
            if not user_id:
                return jsonify({
                    "success": False,
                    "message": "Not authenticated"
                }), 401

            user = get_auth_service().user_model.get_user_by_id(user_id)
            if not user:
                session.clear()
                return jsonify({
                    "success": False,
                    "message": "User not found"
                }), 404

            return jsonify({
                "success": True,
                "user": {
                    "id": user["id"],
                    "email": user["email"],
                    "full_name": user["full_name"],
                    "company_name": user["company_name"],
                    "employee_id": user.get("employee_id")
                }
            }), 200

        except Exception as e:
            logger.error(f"Error getting user: {e}")
            return jsonify({
                "success": False,
                "message": f"An error occurred: {str(e)}"
            }), 500

    @app.route('/api/user/profile', methods=["GET", "PUT"])
    def user_profile():
        from flask import request
        auth_service = get_auth_service()
        user_id = None
        # --- JWT AUTH SUPPORT ---
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ', 1)[1]
            user = auth_service.verify_token(token)
            if user:
                user_id = user.get('id')
        # --- SESSION FALLBACK ---
        if not user_id:
            user_id = session.get('user_id')
        user_model = get_user_model()  # Use singleton
        if request.method == "GET":
            if not user_id:
                return jsonify({"success": False, "message": "Not authenticated", "profile": None}), 401
            user = user_model.get_user_by_id(user_id)
            if not user:
                return jsonify({"success": False, "message": "User not found", "profile": None}), 404
            return jsonify({
                "success": True,
                "profile": {
                    "fullName": user.get("full_name", ""),
                    "email": user.get("email", ""),
                    "employeeId": user.get("employee_id", "")
                }
            })
        elif request.method == "PUT":
            if not user_id:
                return jsonify({"success": False, "message": "Not authenticated"}), 401
            data = request.json
            full_name = data.get("full_name")
            email = data.get("email")
            employee_id = data.get("employee_id")
            # Update user in DB
            user = user_model.get_user_by_id(user_id)
            if not user:
                return jsonify({"success": False, "message": "User not found"}), 404
            try:
                with user_model.db._get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        UPDATE users SET full_name = ?, email = ?, employee_id = ? WHERE id = ?
                    ''', (full_name, email, employee_id, user_id))
                    conn.commit()
                return jsonify({"success": True, "message": "Profile updated"})
            except Exception as e:
                return jsonify({"success": False, "message": f"Failed to update profile: {str(e)}"}), 500

    @app.route("/api/hr-representatives", methods=["GET"])
    def get_hr_representatives():
        """Get list of HR representatives."""
        try:
            hr_reps = [
                {"id": "hr1", "name": "John Smith", "email": "<EMAIL>", "department": "HR Operations"},
                {"id": "hr2", "name": "Sarah Johnson", "email": "<EMAIL>", "department": "Employee Relations"},
                {"id": "hr3", "name": "Michael Brown", "email": "<EMAIL>", "department": "Benefits"}
            ]
            return jsonify({"success": True, "representatives": hr_reps})
        except Exception as e:
            logger.error(f"Error getting HR representatives: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/submit-escalation", methods=["POST"])
    def submit_escalation():
        """Submit a new HR escalation."""
        try:
            data = request.json
            required_fields = ["hrPerson", "issueType", "issueDescription", "priority"]

            for field in required_fields:
                if field not in data:
                    return jsonify({
                        "success": False,
                        "message": f"Missing required field: {field}"
                    }), 400

            user_details = session.get("user_details", {})
            if not user_details:
                return jsonify({
                    "success": False,
                    "message": "User not authenticated"
                }), 401

            escalation_data = {
                "user_id": user_details.get("id"),
                "user_name": user_details.get("name"),
                "user_email": user_details.get("email"),
                "hr_person": data["hrPerson"],
                "issue_type": data["issueType"],
                "description": data["issueDescription"],
                "priority": data["priority"],
                "status": "pending",
                "created_at": time.time()
            }

            logger.info(f"New escalation created: {escalation_data}")

            if ENABLE_EMAIL_ESCALATION:
                email_service = get_email_service()
                hr_email = next((rep["email"] for rep in get_hr_representatives().json["representatives"]
                               if rep["id"] == data["hrPerson"]), None)

                if hr_email:
                    email_service.send_escalation_email(
                        hr_emails=[hr_email],
                        user_query=f"New HR Escalation: {data['issueType']} - {data['issueDescription']}",
                        conversation_history=[{
                            "user_query": f"Priority: {data['priority']}\nIssue Type: {data['issueType']}\nDescription: {data['issueDescription']}",
                            "assistant_response": f"Escalated to: {data['hrPerson']}"
                        }]
                    )

            return jsonify({
                "success": True,
                "message": "Your issue has been escalated to HR. They will contact you shortly.",
                "escalation_id": f"ESC-{int(time.time())}"
            })

        except Exception as e:
            logger.error(f"Error submitting escalation: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/chats/<chat_id>", methods=["GET"])
    def get_chat(chat_id):
        """Get paginated messages for a specific chat."""
        try:
            page = int(request.args.get("page", 1))
            page_size = int(request.args.get("page_size", 20))

            history_manager = get_history_manager()
            messages = history_manager.get_chat_messages(chat_id, page, page_size)

            total_messages = history_manager.get_chat_message_count(chat_id)

            return jsonify({
                "success": True,
                "messages": messages,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_messages": total_messages,
                    "total_pages": (total_messages + page_size - 1) // page_size
                }
            })

        except Exception as e:
            logger.error(f"Error getting chat messages: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/chats/<chat_id>/count", methods=["GET"])
    def get_chat_message_count_api(chat_id):
        """Get total number of messages for a specific chat."""
        try:
            history_manager = get_history_manager()
            total_messages = history_manager.get_chat_message_count(chat_id)
            return jsonify({
                "success": True,
                "count": total_messages
            })
        except Exception as e:
            logger.error(f"Error getting chat message count: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route('/api/queries')
    def get_queries():
        # Dummy data since no query_logs.json exists
        queries = [
            {"id": 1, "text": "How to apply for leave?", "time": "2 min ago"},
            {"id": 2, "text": "Payroll status for June", "time": "10 min ago"},
            {"id": 3, "text": "Update contact info", "time": "1 hr ago"},
        ]
        return jsonify(queries)

    @app.route('/api/chatlogs')
    def get_chatlogs():
        # Dummy data since no chat_logs.json exists
        chats = [
            {"id": 1, "user": "Alice", "msg": "Can I get my payslip?", "tag": "Payroll", "time": "Today"},
            {"id": 2, "user": "Bob", "msg": "Leave balance?", "tag": "Leave", "time": "Yesterday"},
        ]
        return jsonify(chats)

    @app.route('/api/overrides')
    def get_overrides():
        overrides = [
            {"id": 1, "pattern": "leave policy", "response": "Refer to HR portal"},
            {"id": 2, "pattern": "salary slip", "response": "Contact payroll"},
        ]
        return jsonify(overrides)

    @app.route('/api/upload', methods=['POST'])
    def upload_doc():
        file = request.files.get('file')
        if not file or file.filename == '':
            return jsonify({'status': 'error', 'message': 'No file selected'}), 400
        # Save file logic here (not implemented)
        return jsonify({'status': 'success', 'filename': file.filename})

    @app.route('/api/all-chats', methods=['GET'])
    def get_all_chats():
        """
        Get all chatbot conversations, with optional filters for user/session (device_id), date range, and pagination.
        Query params:
            device_id: filter by user/session (optional)
            start_date: ISO string (optional)
            end_date: ISO string (optional)
            limit: int (default 100)
            offset: int (default 0)
        Returns:
            JSON list of conversations
        """
        device_id = request.args.get('device_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        try:
            limit = int(request.args.get('limit', 100))
            offset = int(request.args.get('offset', 0))
        except ValueError:
            limit = 100
            offset = 0
        from src.database.conversation_store import ConversationStore
        store = ConversationStore()
        conversations = store.get_all_conversations(device_id=device_id, start_date=start_date, end_date=end_date, limit=limit, offset=offset)
        return jsonify(conversations)

    @app.route('/api/chat-trends', methods=['GET'])
    @app.route('/chat-trends', methods=['GET'])
    def chat_trends():
        """Returns daily chat counts for the last 30 days using real data from the conversations table."""
        conv_model = ConversationModel()
        today = datetime.now().date()
        days = [(today - timedelta(days=i)) for i in range(29, -1, -1)]
        day_strs = [d.strftime('%Y-%m-%d') for d in days]
        chats_per_day = Counter()
        all_convs = conv_model.get_conversations(limit=50000)
        for c in all_convs:
            dt = c.get('query_timestamp', '')[:10]
            if dt in day_strs:
                chats_per_day[dt] += 1
        data = [{"date": d, "count": chats_per_day[d]} for d in day_strs]
        return jsonify(data)

    @app.route('/api/chat-types', methods=['GET'])
    @app.route('/chat-types', methods=['GET'])
    def chat_types():
        """Returns sentiment/type distribution (mocked as language for now)."""
        conv_model = ConversationModel()
        all_convs = conv_model.get_conversations(limit=10000)
        type_counter = Counter(c.get('language', 'en') for c in all_convs)
        data = [{"name": k, "value": v} for k, v in type_counter.items()]
        return jsonify(data)

    @app.route('/api/queries', methods=['GET'])
    @app.route('/queries', methods=['GET'])
    def queries():
        """Returns top user questions."""
        conv_model = ConversationModel()
        all_convs = conv_model.get_conversations(limit=10000)
        question_counter = Counter(c.get('user_query', '') for c in all_convs)
        last_asked = {}
        intent_map = {}
        for c in all_convs:
            q = c.get('user_query', '')
            last_asked[q] = c.get('query_timestamp', '')
            intent_map[q] = c.get('intent') or q.split()[0]
        top = question_counter.most_common(10)
        data = [{
            "question": q,
            "count": count,
            "lastAsked": last_asked[q],
            "intent": intent_map[q]
        } for q, count in top]
        return jsonify(data)

    @app.route('/api/login', methods=["POST"])
    @app.route('/login', methods=["POST"])
    def login():
        try:
            data = request.json
            email = data.get("email")
            password = data.get("password")
            two_fa_code = data.get("two_fa_code")
            if not email or not password:
                return jsonify({"success": False, "message": "Email and password are required"}), 400
            auth_service = get_auth_service()
            result = auth_service.login_user(email, password, two_fa_code)
            if result.get("success"):
                session["user_id"] = result["user"]["id"]
                session["email"] = result["user"]["email"]
                return jsonify(result), 200
            else:
                # If 2FA required, store user ID for pending verification
                if result.get("message") == "2FA code required":
                    user_id_from_result = result.get("user_id")
                    if user_id_from_result:
                        session["pending_user_id"] = user_id_from_result
                    return jsonify(result), 401
                return jsonify(result), 401
        except Exception as e:
            import traceback
            print(traceback.format_exc())
            return jsonify({"success": False, "message": f"An error occurred: {str(e)}"}), 500

    @app.route('/api/logout', methods=['POST'])
    def logout():
        session.clear()
        return jsonify({"success": True})

    @app.route("/api/leave-balance", methods=["GET"])
    def get_leave_balance():
        email = request.args.get("email")
        employee_id = request.args.get("employee_id")
        user_id = session.get("user_id")
        print(f"[DEBUG] /api/leave-balance: user_id={user_id}, email(param)={email}, employee_id(param)={employee_id}, session={dict(session)}")
        if not email or not employee_id:
            # Try to get from session user_id
            if user_id:
                user = get_auth_service().user_model.get_user_by_id(user_id)
                print(f"[DEBUG] /api/leave-balance: user from DB={user}")
                if user:
                    email = email or user.get("email")
                    employee_id = employee_id or user.get("employee_id")
        if not email or not employee_id:
            print(f"[DEBUG] /api/leave-balance: MISSING email or employee_id after all attempts. Returning error.")
            return jsonify({
                "success": False,
                "message": "Email and employee ID are required. Please complete your info in Settings."
            }), 400
        try:
            url = f"{MOCKAPI_URL}?employee_id={employee_id}&email={email}"
            resp = requests.get(url, timeout=10)
            if resp.status_code == 200:
                data = resp.json()
                # --- PATCH: handle array response from mockapi ---
                if isinstance(data, list) and data:
                    data = data[0]
                if not data or not any(k in data for k in ("paid_leave", "sick_leave", "casual_leave")):
                    return jsonify({
                        "success": False,
                        "message": "We couldn’t find your leave records. Please check your info in Settings and enter correct information."
                    }), 404
                # --- Add chatbot-friendly response field ---
                response_msg = f"Hi {data.get('name', '')}, your leave balance is: Paid Leave: {data.get('paid_leave', 0)}, Sick Leave: {data.get('sick_leave', 0)}, Casual Leave: {data.get('casual_leave', 0)}."
                return jsonify({
                    "success": True,
                    "response": response_msg,
                    "paid_leave": data.get("paid_leave", 0),
                    "sick_leave": data.get("sick_leave", 0),
                    "casual_leave": data.get("casual_leave", 0),
                    "name": data.get("name", "")
                })
            else:
                return jsonify({
                    "success": False,
                    "message": "We couldn’t find your leave records. Please check your info in Settings and enter correct information."
                }), 404
        except Exception as e:
            return jsonify({
                "success": False,
                "message": "Something went wrong while fetching your leave data. Try again later.",
                "error": str(e)
            }), 500

    @app.route('/api/roles', methods=['GET'])
    def get_roles():
        # Mock roles for demo; replace with DB query in production
        return jsonify([
            {"id": 1, "name": "Superadmin", "level": 3},
            {"id": 2, "name": "Admin", "level": 2},
            {"id": 3, "name": "Viewer", "level": 1}
        ])

    @app.route('/api/user/metrics', methods=['GET'])
    def get_user_metrics():
        """
        Get user-specific metrics like total chats, average response time, etc.
        """
        try:
            conv_model = ConversationModel()
            user_id = session.get('user_id')
            if not user_id:
                return jsonify({"success": False, "message": "User not authenticated"}), 401

            user = get_user_model().get_user_by_id(user_id)
            if not user:
                return jsonify({"success": False, "message": "User not found"}), 404

            # Get total chats for the user
            total_chats = conv_model.get_chat_message_count_by_user(user_id)

            # Get average response time for the user
            avg_response_time = conv_model.get_average_response_time_by_user(user_id)

            # Get total documents processed by the user
            total_documents = conv_model.get_total_documents_processed_by_user(user_id)

            return jsonify({
                "success": True,
                "total_chats": total_chats,
                "average_response_time": avg_response_time,
                "total_documents": total_documents
            })
        except Exception as e:
            logger.error(f"Error getting user metrics: {e}")
            return jsonify({"error": str(e)}), 500

    def simple_sentiment(text):
        """Very basic sentiment analysis: positive, negative, neutral."""
        if not text:
            return "neutral"
        text = text.lower()
        positive_words = ["good", "great", "excellent", "happy", "love", "awesome", "fantastic", "thanks", "thank you", "helpful", "appreciate"]
        negative_words = ["bad", "terrible", "hate", "awful", "worst", "problem", "issue", "not happy", "disappointed", "angry", "useless", "frustrated"]
        for w in positive_words:
            if w in text:
                return "positive"
        for w in negative_words:
            if w in text:
                return "negative"
        return "neutral"

    @app.route('/api/chat-analytics/live', methods=['GET'])
    def chat_analytics_live():
        # --- Sessions ---
        session_model = SessionModel()
        active_sessions = session_model.get_active_sessions(user_type="chatbot_user")
        active_users = len(set(s['user_id'] for s in active_sessions if s['user_id']))

        # --- Conversations ---
        conv_model = ConversationModel()
        all_convs = conv_model.get_conversations(limit=50000)
        now = datetime.now()
        last_30_days = [(now - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(29, -1, -1)]
        chats_per_day = Counter()
        unique_questions = set()
        total_queries = 0
        total_duration = 0
        duration_count = 0
        feedbacks = []
        response_times = defaultdict(list)

        # --- New: Sentiment, Intent, Trending, Top Questions ---
        sentiment_counter = Counter()
        intent_counter = Counter()
        intent_samples = defaultdict(list)
        trending_by_day = {d: Counter() for d in last_30_days}
        question_counter = Counter()
        # intent_classifier = IntentClassifier()  # REMOVE

        for c in all_convs:
            dt = c.get('query_timestamp', '')[:10]
            if dt in last_30_days:
                chats_per_day[dt] += 1
            total_queries += 1
            user_query = c.get('user_query', '').strip()
            unique_questions.add(user_query.lower())
            question_counter[user_query] += 1
            # Chat duration
            try:
                start = datetime.fromisoformat(c['query_timestamp'])
                end = datetime.fromisoformat(c['response_timestamp'])
                duration = (end - start).total_seconds()
                total_duration += duration
                duration_count += 1
                response_times[dt].append(duration)
            except Exception:
                pass
            # Feedback
            if c.get('feedback') in ('like', 'dislike'):
                feedbacks.append(1 if c['feedback'] == 'like' else 0)
            # Sentiment
            sentiment = c.get('sentiment')
            if not sentiment:
                sentiment = simple_sentiment(user_query or c.get('assistant_response', ''))
            sentiment_counter[sentiment] += 1
            # Intent (ONLY use stored intent)
            intent = c.get('intent') or 'unknown'
            intent_counter[intent] += 1
            if len(intent_samples[intent]) < 3 and user_query not in intent_samples[intent]:
                intent_samples[intent].append(user_query)
            # Trending topics (intent per day)
            if dt in last_30_days:
                trending_by_day[dt][intent] += 1

        # --- Metrics ---
        avg_chat_duration = (total_duration / duration_count) if duration_count else 0
        user_satisfaction = (sum(feedbacks) / len(feedbacks)) if feedbacks else None

        # Response time metrics per day
        resp_time_metrics = []
        for day in last_30_days:
            times = response_times[day]
            if times:
                avg = sum(times) / len(times)
                p95 = sorted(times)[int(0.95 * len(times)) - 1] if len(times) >= 20 else max(times)
                p99 = sorted(times)[int(0.99 * len(times)) - 1] if len(times) >= 100 else max(times)
                resp_time_metrics.append({
                    "timestamp": day,
                    "avg_response_time": avg,
                    "p95_response_time": p95,
                    "p99_response_time": p99
                })
            else:
                resp_time_metrics.append({
                    "timestamp": day,
                    "avg_response_time": 0,
                    "p95_response_time": 0,
                    "p99_response_time": 0
                })

        # --- Sentiment Distribution ---
        total_sentiments = sum(sentiment_counter.values())
        sentiment_distribution = [
            {"sentiment": k, "count": v, "percentage": round(100 * v / total_sentiments, 1) if total_sentiments else 0}
            for k, v in sentiment_counter.items()
        ]
        # Ensure all 3 sentiments are present
        for s in ["positive", "neutral", "negative"]:
            if not any(d["sentiment"] == s for d in sentiment_distribution):
                sentiment_distribution.append({"sentiment": s, "count": 0, "percentage": 0})
        sentiment_distribution.sort(key=lambda x: ["positive", "neutral", "negative"].index(x["sentiment"]))

        # --- Top Intents ---
        top_intents = []
        for intent, count in intent_counter.most_common(5):
            top_intents.append({
                "intent": intent,
                "count": count,
                "samples": intent_samples[intent]
            })

        # --- Trending Topics (top 3 intents, time series) ---
        trending_topics = []
        top_trend_intents = [i for i, _ in intent_counter.most_common(3)]
        for intent in top_trend_intents:
            trend = [trending_by_day[day][intent] for day in last_30_days]
            trending_topics.append({"topic": intent, "trend": trend})

        # --- Top Questions ---
        top_questions = []
        for q, count in question_counter.most_common(10):
            top_questions.append({"question": q, "count": count})

        # Compose response with real analytics data
        result = {
            "total_queries": total_queries,
            "active_users": active_users,
            "avg_sentiment": (sentiment_counter["positive"] - sentiment_counter["negative"]) / total_queries if total_queries else 0,
            "unique_questions": len(unique_questions),
            "chats_per_day": [chats_per_day[day] for day in last_30_days],
            "avg_chat_duration": avg_chat_duration,
            "resolution_rate": 0.95,  # Example value
            "escalation_rate": 0.05,  # Example value
            "user_satisfaction": user_satisfaction,
            "top_intents": top_intents,
            "trending_topics": trending_topics,
            "sentiment_distribution": sentiment_distribution,
            "top_questions": top_questions,
            "response_times": resp_time_metrics,
            "error_rate": 0.01,
            "uptime_percentage": 100
        }
        return jsonify(result)

    def admin_required(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Simple admin check: require ?admin_secret=YOUR_SECRET in query params
            admin_secret = request.args.get('admin_secret')
            if admin_secret != os.getenv('ADMIN_BACKFILL_SECRET', 'changeme'):
                return jsonify({'error': 'Unauthorized'}), 403
            return f(*args, **kwargs)
        return decorated_function

    @app.route('/api/admin/backfill-intents', methods=['POST'])
    @admin_required
    def backfill_intents():
        """
        One-time endpoint to backfill missing intents in conversations.
        Requires ?admin_secret=... for access.
        """
        from src.intent.intent_classifier import IntentClassifier
        conv_model = ConversationModel()
        batch_size = 100
        updated = 0
        skipped = 0
        processed = 0
        total = 0
        classifier = IntentClassifier()
        # Fetch all conversations with missing/empty intent
        all_missing = [c for c in conv_model.get_conversations(limit=50000) if not c.get('intent') or c.get('intent') == '']
        total = len(all_missing)
        for i in range(0, total, batch_size):
            batch = all_missing[i:i+batch_size]
            for c in batch:
                convo_id = c.get('id')
                user_query = c.get('user_query', '')
                if not user_query:
                    skipped += 1
                    continue
                intent = classifier.classify_intent(user_query).get('intent', 'unknown')
                # Only update if intent is missing
                if not c.get('intent') or c.get('intent') == '':
                    try:
                        with conv_model.db._get_connection() as conn:
                            cursor = conn.cursor()
                            cursor.execute('UPDATE conversations SET intent = ? WHERE id = ?', (intent, convo_id))
                            conn.commit()
                        updated += 1
                    except Exception as e:
                        skipped += 1
                else:
                    skipped += 1
                processed += 1
            logger.info(f"[Backfill] Processed {processed}/{total} (updated: {updated}, skipped: {skipped})")
        return jsonify({
            'total_missing': total,
            'processed': processed,
            'updated': updated,
            'skipped': skipped
        })

    @app.route("/api/status/<filename>", methods=["GET"])
    def file_status(filename):
        """Check if the marker file exists for a given filename."""
        processed_dir = Path(app.root_path) / "data" / "processed"
        marker_file = processed_dir / f"{filename}.processed_raw.json"
        exists = marker_file.exists()
        return jsonify({
            "filename": filename,
            "ready_for_query": exists
        })

    @app.route('/api/extract-text', methods=['POST'])
    def extract_text():
        """Extract full or section text from an uploaded document."""
        data = request.json
        file_path = data.get('file_path')
        section = data.get('section')
        if not file_path:
            return jsonify({'error': 'file_path is required'}), 400
        file_path_obj = Path(file_path)
        doc = FileProcessor.process_file(file_path_obj)
        text = doc.get('content', '')
        if section and section.strip():
            # Simple section extraction: look for section header and return following lines
            import re
            pattern = re.compile(rf'{re.escape(section)}[\s\n\r:.-]*((?:.*\n?)+)', re.IGNORECASE)
            match = pattern.search(text)
            if match:
                text = match.group(1).strip()
            else:
                text = ''
        return jsonify({'text': text, 'title': doc.get('title', ''), 'source_file': file_path})

    @app.route('/api/extract-entities', methods=['POST'])
    def extract_entities():
        """Extract NER entities from uploaded document or provided text."""
        data = request.json
        file_path = data.get('file_path')
        text = data.get('text')
        section = data.get('section')
        if file_path and not text:
            file_path_obj = Path(file_path)
            doc = FileProcessor.process_file(file_path_obj)
            text = doc.get('content', '')
            if section and section.strip():
                import re
                pattern = re.compile(rf'{re.escape(section)}[\s\n\r:.-]*((?:.*\n?)+)', re.IGNORECASE)
                match = pattern.search(text)
                if match:
                    text = match.group(1).strip()
                else:
                    text = ''
        if not text or not text.strip():
            return jsonify({'error': 'No text found for entity extraction'}), 400
        extractor = EntityExtractor()
        entities = extractor.extract_entities(text)
        return jsonify({'entities': entities, 'text': text})

    # --- SERVE REACT STATIC FILES FOR FRONTEND ---
    @app.route('/assets/<path:filename>')
    def serve_assets(filename):
        return send_from_directory(os.path.join(app.static_folder, 'assets'), filename)

    @app.route('/favicon.png')
    def favicon():
        return send_from_directory(app.static_folder, 'favicon.png')

    @app.route('/', defaults={'path': ''})
    @app.route('/<path:path>')
    def serve_react(path):
        if path != '' and os.path.exists(os.path.join(app.static_folder, path)):
            return send_from_directory(app.static_folder, path)
        else:
            return send_from_directory(app.static_folder, 'index.html')

    # --- BACKEND-ONLY TEMPLATE ROUTES (if needed) ---
    # @app.route('/escalation')
    # def escalation():
    #     return render_template('escalation_form.html')

    return app

if __name__ == "__main__":
    try:
        initialize_services()
        flask_app = create_app()
        flask_app.run(host="0.0.0.0", port=5051, debug=False)
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        raise
