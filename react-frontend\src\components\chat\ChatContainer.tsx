import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Message, FileAttachment } from '@/types';
import ChatMessages from './ChatMessages';
import ChatInput from './ChatInput';
import HRSuggestionButtons from './HRSuggestionButtons';
import { cn } from '@/lib/utils';
import Logo from '/img/favicon.png';
import { useChat } from '@/hooks/useChat';
import { useRef, useState, useEffect } from 'react';
import { ArrowDown } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ChatContainerProps {
  messages: Message[];
  isLoading: boolean;
  attachedFiles: FileAttachment[];
  onSendMessage: (message: string, files?: FileAttachment[]) => void;
  onAddFile: (file: FileAttachment) => void;
  onRemoveFile: (fileId: string) => void;
  onOpenVoice: () => void;
  onOpenEscalation: () => void;
  summarizeFile?: (file: FileAttachment) => Promise<string>;
  className?: string;
}

const ChatContainer: React.FC<ChatContainerProps> = ({
  messages,
  isLoading,
  attachedFiles,
  onSendMessage,
  onAddFile,
  onRemoveFile,
  onOpenVoice,
  onOpenEscalation,
  summarizeFile,
  className,
}) => {
  // Ref to scroll to bottom
  const chatBottomRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);

  const handleScrollToBottom = () => {
    chatBottomRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleScroll = () => {
    if (!messagesContainerRef.current || !chatBottomRef.current) return;

    const container = messagesContainerRef.current;
    // Check if user is at the bottom (within 1mm tolerance, approximately 3-4 pixels)
    const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 4;

    // Show button only when content overflows and user is not at bottom
    const hasOverflow = container.scrollHeight > container.clientHeight;
    setShowScrollButton(!isAtBottom && hasOverflow && messages.length > 0);
  };

  useEffect(() => {
    const container = messagesContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, []);

  return (
    <div className={cn("flex flex-col h-full bg-white dark:bg-gray-900 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-700 scrollbar-track-gray-100 dark:scrollbar-track-gray-800", className)}>
      {messages.length === 0 ? (
        <div className="flex-1 flex flex-col justify-center items-center h-full px-4 py-6 overflow-y-auto"> {/* py-6 for compact vertical padding */}
          {/* Welcome Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-6 max-w-2xl" // mb-6 for compact margin below welcome section
          >
            <div className="mb-4"> {/* mb-4 for compact margin below logo/text */}
              <img src={Logo} alt="Company Logo" className="w-16 h-16 rounded-3xl mx-auto mb-3 shadow-md" /> {/* mb-4 for compact margin below logo */}
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-1"> {/* mb-2 for compact margin below heading */}
                Welcome to ZiaHR
              </h1>
              <p className="text-base text-gray-600 dark:text-gray-300 leading-snug">
                Your intelligent HR assistant. Ask me anything about company policies, benefits, or procedures.
              </p>
            </div>
          </motion.div>

          {/* HR Suggestion Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="w-full max-w-4xl mb-8"
          >
            <HRSuggestionButtons onSuggestionClick={onSendMessage} />
          </motion.div>

          {/* Chat Input */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="w-full max-w-2xl"
          >
            <ChatInput
              onSendMessage={onSendMessage}
              attachedFiles={attachedFiles}
              onAddFile={onAddFile}
              onRemoveFile={onRemoveFile}
              onOpenVoice={onOpenVoice}
              onOpenEscalation={onOpenEscalation}
              isLoading={isLoading}
              isEmpty={true}
              onSummarizeFile={typeof summarizeFile === 'function' ? summarizeFile : undefined}
            />
          </motion.div>
        </div>
      ) : (
        <div className="relative flex flex-col h-full">
          <div
            ref={messagesContainerRef}
            className="flex-1 overflow-y-auto pr-5"
            onScroll={handleScroll}
          >
            <div className="max-w-3xl mx-auto px-3">
              <ChatMessages
                messages={messages}
                isLoading={isLoading}
                onSuggestionClick={onSendMessage}
                bottomRef={chatBottomRef}
              />
            </div>
          </div>

          {/* Scroll to bottom button - floats above input box, not overlapping */}
          <AnimatePresence>
            {showScrollButton && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="absolute left-1/2 z-20"
                style={{
                  bottom: 'calc(88px + 16px)', // 88px = ChatInput height + margin, 16px gap above input
                  transform: 'translateX(-50%)',
                  pointerEvents: 'auto',
                }}
              >
                <Button
                  variant="secondary"
                  size="icon"
                  onClick={handleScrollToBottom}
                  className="rounded-full shadow-lg hover:shadow-xl transition-all bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700"
                  style={{ width: 29, height: 29, minWidth: 29, minHeight: 29, position: 'relative', zIndex: 30 }}
                  aria-label="Scroll to bottom"
                >
                  <ArrowDown className="h-4 w-4" />
                </Button>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Remove border-t and border classes from this div to eliminate the horizontal line */}
          <div className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm">
            <div className="max-w-2xl mx-auto px-2 py-2">
              <ChatInput
                onSendMessage={onSendMessage}
                attachedFiles={attachedFiles}
                onAddFile={onAddFile}
                onRemoveFile={onRemoveFile}
                onOpenVoice={onOpenVoice}
                onOpenEscalation={onOpenEscalation}
                isLoading={isLoading}
                isEmpty={false}
                onSummarizeFile={typeof summarizeFile === 'function' ? summarizeFile : undefined}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatContainer;
